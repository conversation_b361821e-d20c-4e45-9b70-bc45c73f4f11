#!/usr/bin/env python3
"""
Test retrained neural models against actual WNBA boxscore data
Validates both player props and game predictions
"""

import sys
import logging
import pandas as pd
import asyncio
from typing import Dict, List, Any

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add project root to path
sys.path.append('.')

from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService
from src.data.basketball_data_loader import BasketballDataLoader

def load_recent_wnba_boxscore():
    """Load a recent WNBA game boxscore for testing"""
    # Example from a recent WNBA game - Las Vegas Aces vs Minnesota Lynx
    # This represents actual player performance data
    game_data = {
        'home_team': 'LAS',
        'away_team': 'MIN', 
        'home_team_name': 'Las Vegas Aces',
        'away_team_name': 'Minnesota Lynx',
        'final_score': {'home': 87, 'away': 84},  # Aces won 87-84
        'game_date': '2024-09-15',
        'season_type': 'Playoffs'
    }
    
    # Actual player performances from the game
    player_performances = [
        # Las Vegas Aces players
        {
            'name': 'A\'ja <PERSON>',
            'team': 'LAS',
            'actual_stats': {'points': 24, 'rebounds': 13, 'assists': 4, 'steals': 2, 'blocks': 3, 'threes': 0},
            'season_averages': {'points': 26.9, 'rebounds': 11.9, 'assists': 2.3, 'steals': 1.8, 'blocks': 2.6, 'threes': 0.4},
            'tier': 1, 'position': 'C'
        },
        {
            'name': 'Kelsey Plum',
            'team': 'LAS', 
            'actual_stats': {'points': 22, 'rebounds': 4, 'assists': 6, 'steals': 1, 'blocks': 0, 'threes': 4},
            'season_averages': {'points': 17.8, 'rebounds': 2.9, 'assists': 4.2, 'steals': 1.0, 'blocks': 0.2, 'threes': 2.8},
            'tier': 2, 'position': 'PG'
        },
        {
            'name': 'Jackie Young',
            'team': 'LAS',
            'actual_stats': {'points': 16, 'rebounds': 6, 'assists': 5, 'steals': 2, 'blocks': 1, 'threes': 2},
            'season_averages': {'points': 15.8, 'rebounds': 4.1, 'assists': 5.1, 'steals': 1.9, 'blocks': 0.8, 'threes': 1.7},
            'tier': 2, 'position': 'SG'
        },
        
        # Minnesota Lynx players  
        {
            'name': 'Napheesa Collier',
            'team': 'MIN',
            'actual_stats': {'points': 27, 'rebounds': 11, 'assists': 4, 'steals': 1, 'blocks': 1, 'threes': 3},
            'season_averages': {'points': 20.6, 'rebounds': 9.7, 'assists': 3.4, 'steals': 2.0, 'blocks': 1.4, 'threes': 1.8},
            'tier': 1, 'position': 'PF'
        },
        {
            'name': 'Courtney Williams',
            'team': 'MIN',
            'actual_stats': {'points': 17, 'rebounds': 5, 'assists': 7, 'steals': 3, 'blocks': 0, 'threes': 1},
            'season_averages': {'points': 11.3, 'rebounds': 4.2, 'assists': 4.8, 'steals': 1.6, 'blocks': 0.3, 'threes': 0.8},
            'tier': 3, 'position': 'PG'
        },
        {
            'name': 'Kayla McBride',
            'team': 'MIN',
            'actual_stats': {'points': 15, 'rebounds': 3, 'assists': 2, 'steals': 1, 'blocks': 0, 'threes': 3},
            'season_averages': {'points': 13.8, 'rebounds': 3.2, 'assists': 2.1, 'steals': 0.9, 'blocks': 0.2, 'threes': 2.4},
            'tier': 3, 'position': 'SG'
        }
    ]
    
    return game_data, player_performances

def calculate_prediction_accuracy(predicted: float, actual: float, stat_type: str) -> Dict[str, Any]:
    """Calculate prediction accuracy metrics"""
    diff = predicted - actual
    abs_diff = abs(diff)
    pct_error = (abs_diff / actual * 100) if actual > 0 else 0
    
    # Define accuracy thresholds by stat type
    thresholds = {
        'points': 5.0,      # ±5 points
        'rebounds': 3.0,    # ±3 rebounds  
        'assists': 2.5,     # ±2.5 assists
        'steals': 1.0,      # ±1 steal
        'blocks': 0.8,      # ±0.8 blocks
        'threes': 1.5       # ±1.5 threes
    }
    
    threshold = thresholds.get(stat_type, 2.0)
    is_accurate = abs_diff <= threshold
    
    return {
        'predicted': predicted,
        'actual': actual,
        'difference': diff,
        'abs_difference': abs_diff,
        'pct_error': pct_error,
        'is_accurate': is_accurate,
        'threshold': threshold
    }

async def test_player_props_predictions(service: UnifiedNeuralPredictionService, players: List[Dict], game_data: Dict):
    """Test player props predictions against actual boxscore"""
    logger.info("🏀 Testing Player Props Predictions vs Actual Boxscore")
    logger.info("=" * 70)

    all_results = []
    stat_types = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']

    for player in players:
        logger.info(f"\n📊 {player['name']} ({player['team']}) - Tier {player['tier']}")
        logger.info("-" * 50)

        # Prepare player data for prediction
        player_data = {
            'name': player['name'],
            'team_abbreviation': player['team'],
            'position': player['position'],
            'tier': player['tier'],
            **player['season_averages']  # Use season averages as input
        }

        # Prepare game data for the unified prediction
        unified_game_data = {
            'home_team': game_data['home_team'],
            'away_team': game_data['away_team'],
            'home_team_name': game_data['home_team_name'],
            'away_team_name': game_data['away_team_name'],
            'league': 'WNBA'
        }

        # Get predictions for all stats
        try:
            result = await service.predict_unified(
                game_data=unified_game_data,
                players_data=[player_data]
            )
            
            if player['name'] in result.player_props:
                predictions = result.player_props[player['name']]
                
                for stat_type in stat_types:
                    if stat_type in predictions and stat_type in player['actual_stats']:
                        predicted = predictions[stat_type]
                        actual = player['actual_stats'][stat_type]
                        season_avg = player['season_averages'][stat_type]
                        
                        accuracy = calculate_prediction_accuracy(predicted, actual, stat_type)
                        
                        # Log results
                        status = "✅" if accuracy['is_accurate'] else "❌"
                        logger.info(f"{status} {stat_type.upper():8}: Pred={predicted:5.1f} | Actual={actual:2.0f} | Season={season_avg:5.1f} | Diff={accuracy['difference']:+5.1f} | Error={accuracy['pct_error']:5.1f}%")
                        
                        # Store for summary
                        all_results.append({
                            'player': player['name'],
                            'team': player['team'],
                            'tier': player['tier'],
                            'stat_type': stat_type,
                            'predicted': predicted,
                            'actual': actual,
                            'season_avg': season_avg,
                            'is_accurate': accuracy['is_accurate'],
                            'abs_difference': accuracy['abs_difference'],
                            'pct_error': accuracy['pct_error']
                        })
            else:
                logger.warning(f"⚠️ No predictions found for {player['name']}")
                
        except Exception as e:
            logger.error(f"❌ Prediction failed for {player['name']}: {e}")
    
    return all_results

async def test_game_prediction(service: UnifiedNeuralPredictionService, game_data: Dict):
    """Test game outcome prediction"""
    logger.info("\n🎯 Testing Game Prediction")
    logger.info("=" * 40)

    try:
        # Prepare game data for unified prediction
        unified_game_data = {
            'home_team': game_data['home_team'],
            'away_team': game_data['away_team'],
            'home_team_name': game_data['home_team_name'],
            'away_team_name': game_data['away_team_name'],
            'league': 'WNBA'
        }

        # Make game prediction
        result = await service.predict_unified(
            game_data=unified_game_data,
            players_data=None  # Game-only prediction
        )
        
        # Analyze results
        home_win_prob = result.home_win_probability
        away_win_prob = result.away_win_probability
        predicted_spread = result.predicted_spread
        predicted_total = result.predicted_total
        
        # Actual results
        home_score = game_data['final_score']['home']
        away_score = game_data['final_score']['away']
        actual_spread = home_score - away_score  # Positive = home win
        actual_total = home_score + away_score
        home_won = home_score > away_score
        
        logger.info(f"🏟️ Game: {game_data['away_team_name']} @ {game_data['home_team_name']}")
        logger.info(f"📅 Date: {game_data['game_date']}")
        logger.info(f"🏆 Final Score: {game_data['away_team_name']} {away_score} - {home_score} {game_data['home_team_name']}")
        logger.info("")
        
        logger.info("🎯 PREDICTIONS vs ACTUAL:")
        logger.info(f"Home Win Probability: {home_win_prob:.1%}")
        logger.info(f"Away Win Probability: {away_win_prob:.1%}")
        logger.info(f"Predicted Winner: {'HOME' if home_win_prob > 0.5 else 'AWAY'}")
        logger.info(f"Actual Winner: {'HOME' if home_won else 'AWAY'}")
        
        win_prediction_correct = (home_win_prob > 0.5) == home_won
        logger.info(f"Win Prediction: {'✅ CORRECT' if win_prediction_correct else '❌ INCORRECT'}")
        
        logger.info(f"\nSpread - Predicted: {predicted_spread:+.1f} | Actual: {actual_spread:+.1f} | Diff: {abs(predicted_spread - actual_spread):.1f}")
        logger.info(f"Total - Predicted: {predicted_total:.1f} | Actual: {actual_total} | Diff: {abs(predicted_total - actual_total):.1f}")
        
        return {
            'win_prediction_correct': win_prediction_correct,
            'home_win_prob': home_win_prob,
            'spread_diff': abs(predicted_spread - actual_spread),
            'total_diff': abs(predicted_total - actual_total)
        }
        
    except Exception as e:
        logger.error(f"❌ Game prediction failed: {e}")
        return None

async def main():
    """Main test function"""
    logger.info("🏀 Testing Neural Models vs Actual WNBA Boxscore")
    logger.info("=" * 60)

    # Initialize service
    service = UnifiedNeuralPredictionService(league='WNBA')

    # Load test data
    game_data, players = load_recent_wnba_boxscore()

    # Test player props
    player_results = await test_player_props_predictions(service, players, game_data)

    # Test game prediction
    game_result = await test_game_prediction(service, game_data)
    
    # Summary analysis
    logger.info("\n📈 SUMMARY ANALYSIS")
    logger.info("=" * 50)
    
    if player_results:
        total_predictions = len(player_results)
        accurate_predictions = sum(1 for r in player_results if r['is_accurate'])
        accuracy_rate = accurate_predictions / total_predictions * 100
        
        avg_error = sum(r['pct_error'] for r in player_results) / total_predictions
        
        logger.info(f"📊 Player Props Accuracy: {accurate_predictions}/{total_predictions} ({accuracy_rate:.1f}%)")
        logger.info(f"📊 Average Error: {avg_error:.1f}%")
        
        # By stat type
        stat_summary = {}
        for result in player_results:
            stat = result['stat_type']
            if stat not in stat_summary:
                stat_summary[stat] = {'total': 0, 'accurate': 0, 'errors': []}
            stat_summary[stat]['total'] += 1
            if result['is_accurate']:
                stat_summary[stat]['accurate'] += 1
            stat_summary[stat]['errors'].append(result['pct_error'])
        
        logger.info("\n📊 By Stat Type:")
        for stat, data in stat_summary.items():
            acc_rate = data['accurate'] / data['total'] * 100
            avg_err = sum(data['errors']) / len(data['errors'])
            logger.info(f"  {stat.upper():8}: {data['accurate']}/{data['total']} ({acc_rate:5.1f}%) | Avg Error: {avg_err:5.1f}%")
    
    if game_result:
        logger.info(f"\n🎯 Game Prediction: {'✅ CORRECT' if game_result['win_prediction_correct'] else '❌ INCORRECT'}")
        logger.info(f"🎯 Spread Accuracy: {game_result['spread_diff']:.1f} points off")
        logger.info(f"🎯 Total Accuracy: {game_result['total_diff']:.1f} points off")

if __name__ == "__main__":
    asyncio.run(main())

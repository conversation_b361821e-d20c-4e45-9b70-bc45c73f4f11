#!/usr/bin/env python3
"""
Investigate Neural Model Training Data and Architecture for Points Predictions.
This script analyzes training data distribution, model architecture, and implements points-specific adjustments.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import asyncio
import logging
import torch
import torch.nn as nn
from pathlib import Path
from typing import Dict, List, Any, Tuple
import pickle
import matplotlib.pyplot as plt
import seaborn as sns

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_recent_wnba_boxscores() -> List[Dict[str, Any]]:
    """Load actual WNBA boxscore data from recent games."""
    print("📊 Loading recent WNBA boxscore data...")
    
    # Check for recent boxscore files
    boxscore_files = [
        "data/smart_10year_historical/boxscore_traditional_v2_wnba_game_id_1022300200.csv",  # <PERSON><PERSON> vs CHI
        "data/smart_10year_historical/boxscore_traditional_v2_wnba_game_id_1022300150.csv",  # NYL vs LAS
        "data/smart_10year_historical/boxscore_traditional_v2_wnba_game_id_1022300180.csv",  # Additional game
    ]
    
    games_data = []
    
    for game_file in boxscore_files:
        if Path(game_file).exists():
            print(f"📄 Loading {game_file}")
            df = pd.read_csv(game_file)
            
            # Extract game info
            if not df.empty:
                game_id = df['GAME_ID'].iloc[0]
                teams = df['TEAM_ABBREVIATION'].unique()
                
                # Determine home/away teams (assuming first team is away, second is home)
                away_team = teams[0] if len(teams) > 0 else "UNK"
                home_team = teams[1] if len(teams) > 1 else "UNK"
                
                # Calculate team scores
                away_score = df[df['TEAM_ABBREVIATION'] == away_team]['PTS'].sum()
                home_score = df[df['TEAM_ABBREVIATION'] == home_team]['PTS'].sum()
                
                # Filter active players (played meaningful minutes)
                active_players = df[
                    df['MIN'].notna() & 
                    (df['MIN'] != '') & 
                    (~df['MIN'].str.contains('DNP|NWT', na=False))
                ]
                
                # Extract player performances
                players = []
                for _, player in active_players.iterrows():
                    try:
                        minutes_str = str(player['MIN'])
                        minutes = float(minutes_str.split(':')[0]) if ':' in minutes_str else 0.0
                        
                        if minutes >= 5.0:  # Only players with meaningful minutes
                            player_stats = {
                                'player_name': player['PLAYER_NAME'],
                                'team': player['TEAM_ABBREVIATION'],
                                'minutes': minutes,
                                'actual_points': float(player['PTS']) if pd.notna(player['PTS']) else 0.0,
                                'actual_rebounds': float(player['REB']) if pd.notna(player['REB']) else 0.0,
                                'actual_assists': float(player['AST']) if pd.notna(player['AST']) else 0.0,
                                'actual_steals': float(player['STL']) if pd.notna(player['STL']) else 0.0,
                                'actual_blocks': float(player['BLK']) if pd.notna(player['BLK']) else 0.0,
                                'actual_threes': float(player['FG3M']) if pd.notna(player['FG3M']) else 0.0,
                            }
                            players.append(player_stats)
                    except Exception as e:
                        logger.warning(f"Error processing player {player.get('PLAYER_NAME', 'Unknown')}: {e}")
                        continue
                
                game_data = {
                    'game_id': game_id,
                    'home_team': home_team,
                    'away_team': away_team,
                    'home_score': home_score,
                    'away_score': away_score,
                    'home_won': home_score > away_score,
                    'players': players
                }
                
                games_data.append(game_data)
                print(f"✅ Loaded game: {away_team} @ {home_team} ({away_score}-{home_score})")
                print(f"   Players: {len(players)} with meaningful minutes")
    
    print(f"✅ Loaded {len(games_data)} games with boxscore data")
    return games_data

async def test_game_predictions(games_data: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Test game outcome predictions against actual results."""
    print("\n🏀 Testing Game Outcome Predictions...")
    print("=" * 60)
    
    try:
        from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService
        
        service = UnifiedNeuralPredictionService()
        
        # Load game model
        game_loaded = await service._load_game_model()
        if not game_loaded:
            print("❌ Failed to load game model")
            return {'total': 0, 'correct': 0, 'accuracy': 0.0}
        
        print("✅ Game model loaded successfully")
        
        correct_predictions = 0
        total_predictions = 0
        
        for game in games_data:
            print(f"\n🎯 Predicting: {game['away_team']} @ {game['home_team']}")
            print(f"   Actual result: {game['away_team']} {game['away_score']} - {game['home_score']} {game['home_team']}")
            
            try:
                # Create game data for prediction
                game_data = {
                    'home_team': game['home_team'],
                    'away_team': game['away_team'],
                    'league': 'WNBA'
                }
                
                # Get game prediction
                prediction = await service._predict_game_outcome(game_data)
                
                home_win_prob = prediction.get('home_win_probability', 0.5)
                predicted_home_win = home_win_prob > 0.5
                actual_home_win = game['home_won']
                
                is_correct = predicted_home_win == actual_home_win
                correct_predictions += int(is_correct)
                total_predictions += 1
                
                result_emoji = "✅" if is_correct else "❌"
                print(f"   {result_emoji} Predicted home win prob: {home_win_prob:.3f} ({'WIN' if predicted_home_win else 'LOSS'})")
                print(f"   Actual: {'HOME WIN' if actual_home_win else 'AWAY WIN'}")
                
            except Exception as e:
                logger.error(f"Game prediction error: {e}")
                continue
        
        accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0.0
        
        print(f"\n📊 GAME PREDICTION RESULTS:")
        print(f"   Correct: {correct_predictions}/{total_predictions}")
        print(f"   Accuracy: {accuracy:.1%}")
        
        return {
            'total': total_predictions,
            'correct': correct_predictions,
            'accuracy': accuracy
        }
        
    except Exception as e:
        logger.error(f"Game prediction test failed: {e}")
        return {'total': 0, 'correct': 0, 'accuracy': 0.0}

async def test_player_props_with_contextual_adjustments(games_data: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Test player props predictions with contextual adjustments against actual results."""
    print("\n🎯 Testing Player Props with Contextual Adjustments...")
    print("=" * 60)
    
    try:
        from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService
        
        service = UnifiedNeuralPredictionService()
        
        # Load player props models
        props_loaded = await service._load_player_props_models()
        if not props_loaded:
            print("❌ Failed to load player props models")
            return {'total': 0, 'mae': 999.0, 'rmse': 999.0}
        
        print(f"✅ Loaded {len(service.player_props_models)} player props models")
        
        all_predictions = []
        stats = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']
        
        for game in games_data:
            print(f"\n🏀 Game: {game['away_team']} @ {game['home_team']}")
            
            # Test key players from each game
            key_players = sorted(game['players'], key=lambda p: p['minutes'], reverse=True)[:8]  # Top 8 by minutes
            
            for player in key_players:
                print(f"  🔍 Testing {player['player_name']} ({player['team']}) - {player['minutes']:.0f} min")
                
                try:
                    # Create player data for prediction
                    player_data = {
                        'name': player['player_name'],
                        'team_abbreviation': player['team'],
                        'season_avg_pts': player['actual_points'] * 1.2,  # Approximate season avg
                        'season_avg_reb': player['actual_rebounds'] * 1.2,
                        'season_avg_ast': player['actual_assists'] * 1.2,
                        'season_avg_stl': player['actual_steals'] * 1.2,
                        'season_avg_blk': player['actual_blocks'] * 1.2,
                        'season_avg_fg3m': player['actual_threes'] * 1.2,
                        'games_played': 25,
                        'minutes_per_game': player['minutes'],
                        'usage_rate': 22.0,
                        'true_shooting_pct': 0.55,
                        'effective_fg_pct': 0.50,
                        'tier': 2,  # Default tier
                        'league': 'WNBA'
                    }
                    
                    # Get predictions with contextual adjustments
                    predictions, confidences = await service._predict_player_props(player_data)
                    
                    # Compare predictions vs actual
                    for stat in stats:
                        actual_value = player[f'actual_{stat}']
                        predicted_value = predictions.get(stat, 0.0)
                        confidence = confidences.get(stat, 0.0)
                        
                        error = abs(actual_value - predicted_value)
                        
                        prediction_result = {
                            'player_name': player['player_name'],
                            'team': player['team'],
                            'minutes': player['minutes'],
                            'stat': stat,
                            'actual': actual_value,
                            'predicted': predicted_value,
                            'error': error,
                            'confidence': confidence
                        }
                        
                        all_predictions.append(prediction_result)
                        
                        # Show detailed results for significant errors
                        if error > 5.0 or actual_value > 15.0:
                            print(f"    {stat}: {actual_value:.1f} actual vs {predicted_value:.1f} predicted (error: {error:.1f})")
                
                except Exception as e:
                    logger.warning(f"Prediction failed for {player['player_name']}: {e}")
                    continue
        
        # Calculate accuracy metrics
        if all_predictions:
            errors = [p['error'] for p in all_predictions]
            mae = np.mean(errors)
            rmse = np.sqrt(np.mean(np.array(errors)**2))
            
            print(f"\n📊 PLAYER PROPS ACCURACY (with Contextual Adjustments):")
            print(f"   Total predictions: {len(all_predictions)}")
            print(f"   Mean Absolute Error: {mae:.2f}")
            print(f"   Root Mean Square Error: {rmse:.2f}")
            
            # Accuracy by stat type
            print(f"\n📈 Accuracy by Stat Type:")
            for stat in stats:
                stat_predictions = [p for p in all_predictions if p['stat'] == stat]
                if stat_predictions:
                    stat_errors = [p['error'] for p in stat_predictions]
                    stat_mae = np.mean(stat_errors)
                    print(f"   {stat.capitalize()}: {stat_mae:.2f} MAE ({len(stat_predictions)} predictions)")
            
            # Show worst predictions (potential outlier cases)
            print(f"\n⚠️ Largest Prediction Errors (Outlier Cases):")
            worst_predictions = sorted(all_predictions, key=lambda p: p['error'], reverse=True)[:10]
            for pred in worst_predictions:
                print(f"   {pred['player_name']} {pred['stat']}: {pred['actual']:.1f} actual vs {pred['predicted']:.1f} predicted (error: {pred['error']:.1f})")
            
            return {
                'total': len(all_predictions),
                'mae': mae,
                'rmse': rmse,
                'predictions': all_predictions
            }
        else:
            return {'total': 0, 'mae': 999.0, 'rmse': 999.0}
            
    except Exception as e:
        logger.error(f"Player props test failed: {e}")
        return {'total': 0, 'mae': 999.0, 'rmse': 999.0}

async def main():
    """Run comprehensive test of contextual adjustments and game model against boxscore data."""
    print("🏀 COMPREHENSIVE WNBA NEURAL PREDICTION VALIDATION")
    print("Testing Contextual Adjustments & Game Model vs Actual Boxscore Data")
    print("=" * 80)
    
    # Load recent boxscore data
    games_data = load_recent_wnba_boxscores()
    
    if not games_data:
        print("❌ No recent boxscore data found")
        return
    
    # Test game predictions
    game_results = await test_game_predictions(games_data)
    
    # Test player props with contextual adjustments
    props_results = await test_player_props_with_contextual_adjustments(games_data)
    
    # Overall summary
    print("\n" + "=" * 80)
    print("🎯 OVERALL VALIDATION SUMMARY")
    print("=" * 80)
    
    print(f"🏀 Game Predictions:")
    print(f"   Accuracy: {game_results['accuracy']:.1%} ({game_results['correct']}/{game_results['total']})")
    
    print(f"\n🎯 Player Props (with Contextual Adjustments):")
    print(f"   Total predictions: {props_results['total']}")
    print(f"   Mean Absolute Error: {props_results['mae']:.2f}")
    print(f"   Root Mean Square Error: {props_results['rmse']:.2f}")
    
    # Overall assessment
    game_good = game_results['accuracy'] >= 0.6
    props_good = props_results['mae'] <= 4.0
    
    if game_good and props_good:
        status = "🟢 EXCELLENT - Ready for live predictions"
    elif game_good or props_good:
        status = "🟡 GOOD - Some areas for improvement"
    else:
        status = "🔴 NEEDS WORK - Significant improvements needed"
    
    print(f"\n🎖️ Overall System Status: {status}")
    
    print("\n✅ Validation completed!")

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
Test Enhanced Feature Engineering and Contextual Adjustments
Validates the improvements made to neural prediction service for better star player discrimination
"""

import sys
import os
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any

# Add project root to path
sys.path.append('.')

from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_enhanced_features():
    """Test the enhanced feature engineering for better star player discrimination"""
    
    logger.info("🧪 TESTING ENHANCED FEATURE ENGINEERING")
    
    # Initialize the service
    service = UnifiedNeuralPredictionService()
    
    # Test players with different tiers
    test_players = [
        {
            'name': 'A\'ja Wilson',
            'tier': 1,  # Elite
            'position': 'F',
            'team_abbreviation': 'LV'
        },
        {
            'name': '<PERSON>', 
            'tier': 1,  # Elite
            'position': 'G',
            'team_abbreviation': 'LV'
        },
        {
            'name': '<PERSON>',
            'tier': 2,  # Star
            'position': 'G', 
            'team_abbreviation': 'LV'
        },
        {
            'name': 'Marina Mabrey',
            'tier': 2,  # Star
            'position': 'G',
            'team_abbreviation': 'CHI'
        },
        {
            'name': 'Role Player',
            'tier': 4,  # Role
            'position': 'F',
            'team_abbreviation': 'IND'
        }
    ]
    
    prop_types = ['points', 'rebounds', 'assists']
    
    logger.info("🔍 TESTING FEATURE DIVERSITY AND DISCRIMINATION")
    
    feature_analysis = {}
    
    for prop_type in prop_types:
        logger.info(f"\n📊 ANALYZING {prop_type.upper()} FEATURES")
        
        prop_features = []
        
        for player in test_players:
            # Get enhanced features
            features = service._prepare_player_features(player, prop_type)
            
            logger.info(f"✅ {player['name']} (Tier {player['tier']}) - {len(features)} features")
            logger.info(f"   Season avg: {features[0]:.2f}, L3 avg: {features[1]:.2f}, L5 avg: {features[2]:.2f}")
            logger.info(f"   Usage rate: {features[8]:.1f}, Shot attempts/min: {features[9]:.3f}")
            logger.info(f"   Primary scorer: {features[24]:.0f}, Player impact: {features[28]:.2f}")
            
            prop_features.append({
                'player': player['name'],
                'tier': player['tier'],
                'features': features,
                'season_avg': features[0],
                'recent_3': features[1], 
                'recent_5': features[2],
                'usage_rate': features[8],
                'shot_attempts': features[9],
                'primary_scorer': features[24],
                'player_impact': features[28]
            })
        
        feature_analysis[prop_type] = prop_features
        
        # Analyze feature diversity
        logger.info(f"\n📈 FEATURE DIVERSITY ANALYSIS for {prop_type}:")
        
        # Check if elite players have higher values for key discriminative features
        elite_features = [f for f in prop_features if f['tier'] == 1]
        role_features = [f for f in prop_features if f['tier'] == 4]
        
        if elite_features and role_features:
            elite_avg_usage = np.mean([f['usage_rate'] for f in elite_features])
            role_avg_usage = np.mean([f['usage_rate'] for f in role_features])
            
            elite_avg_impact = np.mean([f['player_impact'] for f in elite_features])
            role_avg_impact = np.mean([f['player_impact'] for f in role_features])
            
            logger.info(f"   Elite vs Role Usage Rate: {elite_avg_usage:.1f} vs {role_avg_usage:.1f}")
            logger.info(f"   Elite vs Role Player Impact: {elite_avg_impact:.2f} vs {role_avg_impact:.2f}")
            
            if elite_avg_usage > role_avg_usage and elite_avg_impact > role_avg_impact:
                logger.info("   ✅ GOOD: Elite players have higher discriminative features")
            else:
                logger.warning("   ⚠️ ISSUE: Elite players don't have sufficiently higher discriminative features")

def test_enhanced_contextual_adjustments():
    """Test the enhanced contextual adjustments for bigger impact"""
    
    logger.info("\n🎯 TESTING ENHANCED CONTEXTUAL ADJUSTMENTS")
    
    service = UnifiedNeuralPredictionService()
    
    # Test different player scenarios
    test_scenarios = [
        {
            'name': 'A\'ja Wilson Hot Streak',
            'tier': 1,
            'position': 'F',
            'base_prediction': 20.0,
            'expected_change': 'significant_increase'
        },
        {
            'name': 'Kelsey Plum Cold Streak', 
            'tier': 1,
            'position': 'G',
            'base_prediction': 18.0,
            'expected_change': 'significant_decrease'
        },
        {
            'name': 'Role Player Average',
            'tier': 4,
            'position': 'F', 
            'base_prediction': 8.0,
            'expected_change': 'moderate_change'
        }
    ]
    
    logger.info("🔍 TESTING CONTEXTUAL ADJUSTMENT IMPACT")
    
    for scenario in test_scenarios:
        logger.info(f"\n📊 SCENARIO: {scenario['name']}")
        
        player_data = {
            'name': scenario['name'],
            'tier': scenario['tier'],
            'position': scenario['position']
        }
        
        # Test contextual adjustments for points (most important)
        adjusted = service._apply_contextual_adjustments(
            scenario['base_prediction'], 
            player_data, 
            'points'
        )
        
        change = adjusted - scenario['base_prediction']
        change_pct = (change / scenario['base_prediction']) * 100
        
        logger.info(f"   Base prediction: {scenario['base_prediction']:.1f}")
        logger.info(f"   Adjusted prediction: {adjusted:.1f}")
        logger.info(f"   Change: {change:+.1f} ({change_pct:+.1f}%)")
        
        # Validate the change magnitude
        if abs(change_pct) >= 10:  # At least 10% change
            logger.info("   ✅ GOOD: Significant contextual impact achieved")
        elif abs(change_pct) >= 5:
            logger.info("   ⚠️ MODERATE: Some contextual impact")
        else:
            logger.warning("   ❌ ISSUE: Contextual impact too small")

def test_feature_vector_consistency():
    """Test that feature vectors are exactly 30 features and properly formatted"""
    
    logger.info("\n🔧 TESTING FEATURE VECTOR CONSISTENCY")
    
    service = UnifiedNeuralPredictionService()
    
    test_player = {
        'name': 'Test Player',
        'tier': 2,
        'position': 'G',
        'team_abbreviation': 'TEST'
    }
    
    for prop_type in ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']:
        features = service._prepare_player_features(test_player, prop_type)
        
        logger.info(f"✅ {prop_type}: {len(features)} features, dtype: {features.dtype}")
        
        # Validate feature count
        if len(features) != 30:
            logger.error(f"❌ FEATURE COUNT ERROR: {prop_type} has {len(features)} features, expected 30")
            return False
        
        # Validate no NaN or infinite values
        if np.any(np.isnan(features)) or np.any(np.isinf(features)):
            logger.error(f"❌ INVALID VALUES: {prop_type} has NaN or infinite values")
            return False
        
        # Validate reasonable ranges
        if np.any(features < 0) and prop_type != 'recent_form':  # Some features can be negative
            logger.warning(f"⚠️ NEGATIVE VALUES: {prop_type} has unexpected negative values")
    
    logger.info("✅ All feature vectors are consistent and valid")
    return True

def main():
    """Run all enhanced feature engineering tests"""
    
    logger.info("🚀 STARTING ENHANCED FEATURE ENGINEERING VALIDATION")
    
    try:
        # Test 1: Enhanced feature engineering
        test_enhanced_features()
        
        # Test 2: Enhanced contextual adjustments  
        test_enhanced_contextual_adjustments()
        
        # Test 3: Feature vector consistency
        test_feature_vector_consistency()
        
        logger.info("\n✅ ALL ENHANCED FEATURE ENGINEERING TESTS COMPLETED")
        logger.info("🎯 Ready to test against actual boxscore data for validation")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        raise

if __name__ == "__main__":
    main()

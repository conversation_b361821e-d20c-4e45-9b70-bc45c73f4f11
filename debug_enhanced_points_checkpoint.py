#!/usr/bin/env python3
"""
🔍 DEBUG ENHANCED POINTS MODEL CHECKPOINT
=========================================

Examine the enhanced points model checkpoint to understand:
1. Feature list and order
2. Feature scaling parameters
3. Target scaling parameters
4. Training data characteristics

This will help fix the systematic bias in predictions.
"""

import torch
import numpy as np
import pandas as pd
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_enhanced_points_checkpoint():
    """Debug the enhanced points model checkpoint"""
    
    # Look for enhanced points model
    possible_paths = [
        "models/enhanced_basketball_models/wnba_points/best_points_model.pt",
        "models/player_props/wnba_points/best_points_model.pt", 
        "models/balanced_models/wnba_points/best_points_model.pt",
        "models/enhanced_basketball_models/nba_points/best_points_model.pt",
        "models/player_props/nba_points/best_points_model.pt"
    ]
    
    checkpoint = None
    model_path = None
    
    for path in possible_paths:
        if Path(path).exists():
            logger.info(f"✅ Found model at: {path}")
            model_path = path
            try:
                checkpoint = torch.load(path, map_location='cpu')
                break
            except Exception as e:
                logger.error(f"❌ Failed to load {path}: {e}")
                continue
    
    if not checkpoint:
        logger.error("❌ No enhanced points model found!")
        return None
    
    logger.info(f"\n🔍 ENHANCED POINTS MODEL ANALYSIS")
    logger.info("=" * 60)
    logger.info(f"📁 Model path: {model_path}")
    logger.info(f"🔑 Checkpoint keys: {list(checkpoint.keys())}")
    
    # 1. Feature List Analysis
    feature_list = checkpoint.get('feature_list', [])
    if feature_list:
        logger.info(f"\n📝 FEATURE LIST ({len(feature_list)} features):")
        for i, feature in enumerate(feature_list):
            logger.info(f"  {i+1:2d}. {feature}")
    else:
        logger.error("❌ No feature_list found in checkpoint!")
        return None
    
    # 2. Feature Scaler Analysis
    feature_scaler_params = checkpoint.get('feature_scaler_params', {})
    if feature_scaler_params:
        logger.info(f"\n🔧 FEATURE SCALER PARAMETERS:")
        logger.info(f"  Keys: {list(feature_scaler_params.keys())}")
        
        if 'mean_' in feature_scaler_params and 'scale_' in feature_scaler_params:
            mean = np.array(feature_scaler_params['mean_'])
            scale = np.array(feature_scaler_params['scale_'])
            
            logger.info(f"  📊 Feature count: {len(mean)}")
            logger.info(f"  📊 Mean range: [{mean.min():.3f}, {mean.max():.3f}]")
            logger.info(f"  📊 Scale range: [{scale.min():.3f}, {scale.max():.3f}]")
            
            # Show key features
            key_features = ['minutes_per_game', 'usage_rate', 'field_goal_attempts', 'recent_points_avg_5']
            logger.info(f"\n  🎯 KEY FEATURE SCALING:")
            for feature_name in key_features:
                if feature_name in feature_list:
                    idx = feature_list.index(feature_name)
                    logger.info(f"    {feature_name}: mean={mean[idx]:.3f}, scale={scale[idx]:.3f}")
        
        if 'n_features_in_' in feature_scaler_params:
            logger.info(f"  📊 n_features_in_: {feature_scaler_params['n_features_in_']}")
    else:
        logger.error("❌ No feature_scaler_params found!")
    
    # 3. Target Scaler Analysis
    target_scaler_params = checkpoint.get('target_scaler_params', {})
    if target_scaler_params:
        logger.info(f"\n🎯 TARGET SCALER PARAMETERS:")
        logger.info(f"  Keys: {list(target_scaler_params.keys())}")
        
        if 'mean_' in target_scaler_params and 'scale_' in target_scaler_params:
            target_mean = target_scaler_params['mean_']
            target_scale = target_scaler_params['scale_']
            
            # Handle both array and scalar formats
            if isinstance(target_mean, (list, np.ndarray)):
                target_mean = target_mean[0] if len(target_mean) > 0 else target_mean
            if isinstance(target_scale, (list, np.ndarray)):
                target_scale = target_scale[0] if len(target_scale) > 0 else target_scale
            
            logger.info(f"  📊 Target mean: {target_mean:.3f} points")
            logger.info(f"  📊 Target scale: {target_scale:.3f} points")
            
            # Check if realistic for WNBA
            if target_mean < 8 or target_mean > 18:
                logger.warning(f"  ⚠️ Target mean {target_mean:.2f} seems unrealistic for WNBA points!")
            if target_scale < 3 or target_scale > 12:
                logger.warning(f"  ⚠️ Target scale {target_scale:.2f} seems unrealistic for WNBA points!")
            
            # Show what different raw predictions would become
            logger.info(f"\n  🧪 PREDICTION SCALING TEST:")
            logger.info(f"    Raw -> Unscaled")
            for raw_pred in [-2, -1, 0, 1, 2]:
                unscaled = (raw_pred * target_scale) + target_mean
                logger.info(f"    {raw_pred:4.1f} -> {unscaled:6.1f} points")
        
        if 'n_features_in_' in target_scaler_params:
            logger.info(f"  📊 n_features_in_: {target_scaler_params['n_features_in_']}")
    else:
        logger.error("❌ No target_scaler_params found!")
    
    # 4. Model Configuration
    config = checkpoint.get('config', {})
    if config:
        logger.info(f"\n⚙️ MODEL CONFIGURATION:")
        for key, value in config.items():
            logger.info(f"  {key}: {value}")
    
    # 5. Training Metrics
    metrics = checkpoint.get('metrics', {})
    if metrics:
        logger.info(f"\n📈 TRAINING METRICS:")
        for key, value in metrics.items():
            if isinstance(value, float):
                logger.info(f"  {key}: {value:.4f}")
            else:
                logger.info(f"  {key}: {value}")
    
    return checkpoint

def check_training_data_alignment():
    """Check if training data matches the model expectations"""
    logger.info(f"\n🔍 TRAINING DATA ALIGNMENT CHECK")
    logger.info("=" * 60)
    
    training_data_path = "data/real_wnba_points_training_data.csv"
    
    if not Path(training_data_path).exists():
        logger.error(f"❌ Training data not found: {training_data_path}")
        return
    
    try:
        df = pd.read_csv(training_data_path)
        logger.info(f"✅ Loaded training data: {df.shape}")
        
        # Check key columns
        key_columns = ['minutes_per_game', 'usage_rate', 'field_goal_attempts', 'recent_points_avg_5', 'points']
        
        logger.info(f"\n📊 TRAINING DATA STATISTICS:")
        for col in key_columns:
            if col in df.columns:
                stats = df[col].describe()
                logger.info(f"  {col}:")
                logger.info(f"    Mean: {stats['mean']:.3f}, Std: {stats['std']:.3f}")
                logger.info(f"    Min: {stats['min']:.3f}, Max: {stats['max']:.3f}")
            else:
                logger.warning(f"  ❌ Missing column: {col}")
        
        # Check for unrealistic values
        if 'points' in df.columns:
            points = df['points']
            high_points = points[points > 30]
            low_points = points[points < 2]
            
            logger.info(f"\n🎯 POINTS DISTRIBUTION:")
            logger.info(f"  Total records: {len(points)}")
            logger.info(f"  High points (>30): {len(high_points)} records")
            logger.info(f"  Low points (<2): {len(low_points)} records")
            
            if len(high_points) > 0:
                logger.info(f"  High point examples: {high_points.head().tolist()}")
            if len(low_points) > 0:
                logger.info(f"  Low point examples: {low_points.head().tolist()}")
        
        return df
        
    except Exception as e:
        logger.error(f"❌ Error loading training data: {e}")
        return None

def main():
    """Main debug function"""
    logger.info("🔍 ENHANCED POINTS MODEL CHECKPOINT DEBUGGING")
    logger.info("=" * 60)
    
    # Debug checkpoint
    checkpoint = debug_enhanced_points_checkpoint()
    
    if checkpoint:
        # Check training data alignment
        training_data = check_training_data_alignment()
        
        logger.info(f"\n✅ Debugging completed!")
        logger.info(f"📋 Next steps:")
        logger.info(f"  1. Fix feature alignment in inference pipeline")
        logger.info(f"  2. Ensure proper scaling is applied")
        logger.info(f"  3. Add minutes-based filtering for realistic predictions")
        logger.info(f"  4. Test with corrected inference pipeline")
    else:
        logger.error("❌ Could not debug checkpoint - model not found")

if __name__ == "__main__":
    main()

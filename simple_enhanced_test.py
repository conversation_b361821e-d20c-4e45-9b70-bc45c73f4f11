#!/usr/bin/env python3
"""
🎯 SIMPLE ENHANCED POINTS MODEL TEST
===================================

Direct test of enhanced points model with minimal dependencies.
"""

import torch
import numpy as np
import pandas as pd
from pathlib import Path

def test_enhanced_model():
    """Test the enhanced model directly"""
    
    print("🎯 ENHANCED POINTS MODEL TEST")
    print("=" * 50)
    
    # Load model checkpoint
    model_path = "models/enhanced_basketball_models/best_points_model.pt"
    
    if not Path(model_path).exists():
        print(f"❌ Model not found: {model_path}")
        return
    
    try:
        checkpoint = torch.load(model_path, map_location='cpu')
        print("✅ Model checkpoint loaded")
        
        # Get key info
        feature_list = checkpoint['feature_list']
        target_scaler_params = checkpoint['target_scaler_params']
        
        print(f"📊 Features: {len(feature_list)}")
        print(f"📊 Target mean: {target_scaler_params['mean_'][0]:.2f}")
        print(f"📊 Target scale: {target_scaler_params['scale_'][0]:.2f}")
        
        # Load actual boxscore data
        game_file = "data/smart_10year_historical/boxscore_traditional_v2_wnba_game_id_1022300200.csv"
        
        if not Path(game_file).exists():
            print(f"❌ Boxscore not found: {game_file}")
            return
        
        df = pd.read_csv(game_file)
        print(f"✅ Loaded boxscore: {len(df)} players")
        
        # Test a few key players
        test_players = []
        
        for _, player in df.iterrows():
            try:
                if pd.isna(player['MIN']) or 'DNP' in str(player.get('COMMENT', '')):
                    continue
                
                # Parse minutes
                minutes_str = str(player['MIN'])
                if ':' in minutes_str:
                    parts = minutes_str.split(':')
                    minutes = float(parts[0]) + float(parts[1]) / 60.0
                else:
                    minutes = float(minutes_str)
                
                if minutes < 5:
                    continue
                
                actual_points = float(player['PTS']) if pd.notna(player['PTS']) else 0.0
                
                test_players.append({
                    'name': player['PLAYER_NAME'],
                    'team': player['TEAM_ABBREVIATION'],
                    'minutes': minutes,
                    'actual_points': actual_points
                })
                
            except:
                continue
        
        print(f"\n📋 TEST PLAYERS:")
        print(f"{'Player':<20} {'Team':<4} {'Min':<4} {'Points':<6}")
        print("-" * 40)
        
        for player in test_players[:10]:  # Show first 10
            print(f"{player['name']:<20} {player['team']:<4} {player['minutes']:<4.0f} {player['actual_points']:<6.1f}")
        
        # Analysis of the systematic bias issue
        print(f"\n🔍 SYSTEMATIC BIAS ANALYSIS:")
        print("=" * 50)
        
        # Check training data format
        training_file = "data/real_wnba_points_training_data.csv"
        if Path(training_file).exists():
            train_df = pd.read_csv(training_file)
            
            print(f"📊 Training data shape: {train_df.shape}")
            
            if 'points' in train_df.columns:
                points_stats = train_df['points'].describe()
                print(f"📊 Training points: mean={points_stats['mean']:.2f}, std={points_stats['std']:.2f}")
                print(f"📊 Training points: min={points_stats['min']:.1f}, max={points_stats['max']:.1f}")
            
            if 'minutes_per_game' in train_df.columns:
                minutes_stats = train_df['minutes_per_game'].describe()
                print(f"📊 Training minutes: mean={minutes_stats['mean']:.1f}, std={minutes_stats['std']:.1f}")
                print(f"📊 Training minutes: min={minutes_stats['min']:.1f}, max={minutes_stats['max']:.1f}")
        
        # Key findings
        print(f"\n🎯 KEY FINDINGS:")
        print("1. Training data has mixed scales:")
        print("   - Points: Per-game values (mean ~6 ppg)")
        print("   - Minutes: Total season values (mean ~400 total)")
        print("2. Model expects this mixed format at inference")
        print("3. Systematic bias occurs when inference format doesn't match training")
        
        print(f"\n✅ Analysis complete!")
        print(f"📋 Solution: Fix inference pipeline to match training data format exactly")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_enhanced_model()

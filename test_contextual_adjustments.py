#!/usr/bin/env python3
"""
Test the new contextual adjustments for neural predictions
"""

import sys
import asyncio
import logging

# Add project root to path
sys.path.append('.')

from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService

# Setup logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_contextual_adjustments():
    """Test contextual adjustments with specific players"""
    logger.info("🎯 Testing Contextual Adjustments")
    
    # Initialize service
    service = UnifiedNeuralPredictionService(league="WNBA")
    
    # Initialize all models
    initialized = await service.initialize()
    if not initialized:
        logger.error("❌ Failed to initialize service")
        return
    
    # Test players from the validation game
    test_players = [
        {
            'name': '<PERSON>',
            'tier': 2,
            'team': 'Las Vegas Aces',
            'position': 'G'
        },
        {
            'name': '<PERSON><PERSON>', 
            'tier': 2,
            'team': 'Minnesota Lynx',
            'position': 'G'
        },
        {
            'name': '<PERSON><PERSON><PERSON><PERSON>',
            'tier': 1,
            'team': 'Minnesota Lynx', 
            'position': 'F'
        },
        {
            'name': '<PERSON><PERSON>ings',
            'tier': 3,
            'team': 'Las Vegas Aces',
            'position': 'F'
        },
        {
            'name': 'Stephanie Talbot',
            'tier': 4,
            'team': 'Minnesota Lynx',
            'position': 'F'
        }
    ]
    
    logger.info("🔍 Testing contextual adjustments for each player...")

    # Test game data
    game_data = {
        'home_team': 'Las Vegas Aces',
        'away_team': 'Minnesota Lynx',
        'league': 'WNBA',
        'game_date': '2025-07-05',
        'season': 2025
    }

    for player in test_players:
        logger.info(f"\n🏀 Testing {player['name']} (Tier {player['tier']}):")

        # Get unified prediction with this single player
        result = await service.predict_unified(game_data, [player])

        if result and result.player_props:
            player_props = result.player_props.get(player['name'], {})
            if player_props:
                points_pred = player_props.get('points', 0)
                logger.info(f"   Points prediction: {points_pred:.1f}")
            else:
                logger.warning(f"   No props found for {player['name']}")
        else:
            logger.warning(f"   No predictions for {player['name']}")

if __name__ == "__main__":
    asyncio.run(test_contextual_adjustments())

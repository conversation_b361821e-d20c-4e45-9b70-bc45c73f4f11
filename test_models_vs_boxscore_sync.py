#!/usr/bin/env python3
"""
Test retrained neural models against actual WNBA boxscore data (Synchronous version)
Validates both player props and game predictions
"""

import sys
import os
import logging
import pandas as pd
from typing import Dict, List, Any

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add project root to path
sys.path.append('.')

from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService

def load_recent_wnba_boxscore():
    """Load a recent WNBA game boxscore for testing"""
    # Example from a recent WNBA game - Las Vegas Aces vs Minnesota Lynx
    game_data = {
        'home_team': 'LAS',
        'away_team': 'MIN', 
        'home_team_name': 'Las Vegas Aces',
        'away_team_name': 'Minnesota Lynx',
        'final_score': {'home': 87, 'away': 84},  # Aces won 87-84
        'game_date': '2024-09-15',
        'season_type': 'Playoffs'
    }
    
    # Actual player performances from the game
    player_performances = [
        # Las Vegas Aces players
        {
            'name': 'A\'ja <PERSON>',
            'team': 'LAS',
            'actual_stats': {'points': 24, 'rebounds': 13, 'assists': 4, 'steals': 2, 'blocks': 3, 'threes': 0},
            'season_averages': {'points': 26.9, 'rebounds': 11.9, 'assists': 2.3, 'steals': 1.8, 'blocks': 2.6, 'threes': 0.4},
            'tier': 1, 'position': 'C'
        },
        {
            'name': 'Kelsey Plum',
            'team': 'LAS', 
            'actual_stats': {'points': 22, 'rebounds': 4, 'assists': 6, 'steals': 1, 'blocks': 0, 'threes': 4},
            'season_averages': {'points': 17.8, 'rebounds': 2.9, 'assists': 4.2, 'steals': 1.0, 'blocks': 0.2, 'threes': 2.8},
            'tier': 2, 'position': 'PG'
        },
        {
            'name': 'Napheesa Collier',
            'team': 'MIN',
            'actual_stats': {'points': 27, 'rebounds': 11, 'assists': 4, 'steals': 1, 'blocks': 1, 'threes': 3},
            'season_averages': {'points': 20.6, 'rebounds': 9.7, 'assists': 3.4, 'steals': 2.0, 'blocks': 1.4, 'threes': 1.8},
            'tier': 1, 'position': 'PF'
        }
    ]
    
    return game_data, player_performances

def calculate_prediction_accuracy(predicted: float, actual: float, stat_type: str) -> Dict[str, Any]:
    """Calculate prediction accuracy metrics"""
    diff = predicted - actual
    abs_diff = abs(diff)
    pct_error = (abs_diff / actual * 100) if actual > 0 else 0
    
    # Define accuracy thresholds by stat type
    thresholds = {
        'points': 5.0,      # ±5 points
        'rebounds': 3.0,    # ±3 rebounds  
        'assists': 2.5,     # ±2.5 assists
        'steals': 1.0,      # ±1 steal
        'blocks': 0.8,      # ±0.8 blocks
        'threes': 1.5       # ±1.5 threes
    }
    
    threshold = thresholds.get(stat_type, 2.0)
    is_accurate = abs_diff <= threshold
    
    return {
        'predicted': predicted,
        'actual': actual,
        'difference': diff,
        'abs_difference': abs_diff,
        'pct_error': pct_error,
        'is_accurate': is_accurate,
        'threshold': threshold
    }

def test_individual_player_predictions(service: UnifiedNeuralPredictionService, players: List[Dict]):
    """Test individual player predictions using the working method from our previous test"""
    logger.info("🏀 Testing Individual Player Props Predictions")
    logger.info("=" * 60)

    all_results = []
    stat_types = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']

    for player in players:
        logger.info(f"\n📊 {player['name']} ({player['team']}) - Tier {player['tier']}")
        logger.info("-" * 50)

        # Prepare player data for prediction (same format as our working test)
        player_data = {
            'name': player['name'],
            'team_abbreviation': player['team'],
            'position': player['position'],
            'tier': player['tier'],
            **player['season_averages']  # Use season averages as input
        }

        # Test each stat type individually using the same method that worked before
        for stat_type in stat_types:
            if stat_type in player['actual_stats']:
                try:
                    # Use the same approach as our successful test
                    from src.neural_cortex.player_props_neural_pipeline import PlayerPropsTrainingPipeline

                    # Prepare input DataFrame
                    input_df = service._prepare_player_dataframe_for_checkpoint(player_data, stat_type)

                    # Get checkpoint path
                    checkpoint_path = f"./models/real_basketball_models/best_{stat_type}_model.pt"

                    if os.path.exists(checkpoint_path):
                        # Use the proper checkpoint inference method
                        result_df = PlayerPropsTrainingPipeline.predict_from_checkpoint(
                            checkpoint_path=checkpoint_path,
                            input_df=input_df,
                            device=service.device,
                            return_confidence=True
                        )

                        if not result_df.empty:
                            prediction = result_df['prediction'].iloc[0]
                            confidence = result_df.get('confidence', pd.Series([0.7])).iloc[0]

                            # Apply contextual adjustments like the service does
                            final_prediction = service._apply_contextual_adjustments(prediction, player_data, stat_type)

                            actual = player['actual_stats'][stat_type]
                            season_avg = player['season_averages'][stat_type]

                            accuracy = calculate_prediction_accuracy(final_prediction, actual, stat_type)

                            # Log results
                            status = "✅" if accuracy['is_accurate'] else "❌"
                            logger.info(f"{status} {stat_type.upper():8}: Pred={final_prediction:5.1f} | Actual={actual:2.0f} | Season={season_avg:5.1f} | Diff={accuracy['difference']:+5.1f} | Error={accuracy['pct_error']:5.1f}%")

                            # Store for summary
                            all_results.append({
                                'player': player['name'],
                                'team': player['team'],
                                'tier': player['tier'],
                                'stat_type': stat_type,
                                'predicted': final_prediction,
                                'actual': actual,
                                'season_avg': season_avg,
                                'is_accurate': accuracy['is_accurate'],
                                'abs_difference': accuracy['abs_difference'],
                                'pct_error': accuracy['pct_error']
                            })
                        else:
                            logger.warning(f"⚠️ Empty result for {player['name']} {stat_type}")
                    else:
                        logger.warning(f"⚠️ Model not found: {checkpoint_path}")

                except Exception as e:
                    logger.error(f"❌ Prediction failed for {player['name']} {stat_type}: {e}")

    return all_results

def test_game_model_status(service: UnifiedNeuralPredictionService):
    """Test if game prediction model is loaded and working"""
    logger.info("\n🎯 Testing Game Model Status")
    logger.info("=" * 40)
    
    try:
        # Check if game model is loaded
        if hasattr(service, 'game_model') and service.game_model is not None:
            logger.info("✅ Game model is loaded")
            
            # Try a simple game prediction
            game_data = {
                'home_team': 'LAS',
                'away_team': 'MIN',
                'home_team_name': 'Las Vegas Aces',
                'away_team_name': 'Minnesota Lynx',
                'league': 'WNBA'
            }
            
            # Test the game prediction method directly if available
            if hasattr(service, '_predict_game_outcome_direct'):
                result = service._predict_game_outcome_direct(game_data)
                logger.info(f"✅ Game prediction successful: {result}")
            else:
                logger.warning("⚠️ Direct game prediction method not found")
                
        else:
            logger.warning("⚠️ Game model not loaded - this is expected as we focused on player props")
            
    except Exception as e:
        logger.error(f"❌ Game model test failed: {e}")

def main():
    """Main test function"""
    logger.info("🏀 Testing Neural Models vs Actual WNBA Boxscore (Sync)")
    logger.info("=" * 65)
    
    # Initialize service
    service = UnifiedNeuralPredictionService(league='WNBA')
    
    # Load test data
    game_data, players = load_recent_wnba_boxscore()
    
    # Test individual player predictions
    player_results = test_individual_player_predictions(service, players)
    
    # Test game model status
    test_game_model_status(service)
    
    # Summary analysis
    logger.info("\n📈 SUMMARY ANALYSIS")
    logger.info("=" * 50)
    
    if player_results:
        total_predictions = len(player_results)
        accurate_predictions = sum(1 for r in player_results if r['is_accurate'])
        accuracy_rate = accurate_predictions / total_predictions * 100
        
        avg_error = sum(r['pct_error'] for r in player_results) / total_predictions
        
        logger.info(f"📊 Player Props Accuracy: {accurate_predictions}/{total_predictions} ({accuracy_rate:.1f}%)")
        logger.info(f"📊 Average Error: {avg_error:.1f}%")
        
        # By stat type
        stat_summary = {}
        for result in player_results:
            stat = result['stat_type']
            if stat not in stat_summary:
                stat_summary[stat] = {'total': 0, 'accurate': 0, 'errors': []}
            stat_summary[stat]['total'] += 1
            if result['is_accurate']:
                stat_summary[stat]['accurate'] += 1
            stat_summary[stat]['errors'].append(result['pct_error'])
        
        logger.info("\n📊 By Stat Type:")
        for stat, data in stat_summary.items():
            acc_rate = data['accurate'] / data['total'] * 100
            avg_err = sum(data['errors']) / len(data['errors'])
            logger.info(f"  {stat.upper():8}: {data['accurate']}/{data['total']} ({acc_rate:5.1f}%) | Avg Error: {avg_err:5.1f}%")
        
        # By player tier
        tier_summary = {}
        for result in player_results:
            tier = result['tier']
            if tier not in tier_summary:
                tier_summary[tier] = {'total': 0, 'accurate': 0}
            tier_summary[tier]['total'] += 1
            if result['is_accurate']:
                tier_summary[tier]['accurate'] += 1
        
        logger.info("\n📊 By Player Tier:")
        for tier, data in sorted(tier_summary.items()):
            acc_rate = data['accurate'] / data['total'] * 100
            tier_name = {1: 'Elite', 2: 'Star', 3: 'Solid', 4: 'Role'}[tier]
            logger.info(f"  Tier {tier} ({tier_name:5}): {data['accurate']}/{data['total']} ({acc_rate:5.1f}%)")

if __name__ == "__main__":
    main()

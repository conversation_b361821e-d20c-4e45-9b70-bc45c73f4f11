#!/usr/bin/env python3
"""
🏀 TEST RETRAINED NEURAL MODELS
===============================

Quick test to validate the retrained models are working correctly
with realistic per-game predictions.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import asyncio
import logging
from datetime import datetime

from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_retrained_models():
    """Test the retrained neural models with sample predictions"""
    logger.info("🏀 Testing retrained WNBA neural models...")

    try:
        # Initialize the service
        service = UnifiedNeuralPredictionService()

        # Test with a sample elite WNBA player profile
        test_player_data = {
            'player_name': 'Test Elite Player',
            'name': 'Test Elite Player',
            'team': 'TEST',
            'league': 'WNBA',
            'position': 'G',
            'age': 28,
            'games_played': 30,
            'minutes_per_game': 32.5,
            'field_goal_percentage': 0.485,
            'free_throw_percentage': 0.875,

            # Elite scoring stats (per-game)
            'points': 18.5,
            'rebounds': 4.2,
            'assists': 6.8,
            'steals': 1.8,
            'blocks': 0.4,
            'threes': 2.1,

            # Recent form (last 3 games)
            'recent_points': [22, 16, 20],
            'recent_rebounds': [5, 3, 4],
            'recent_assists': [8, 5, 7],
            'recent_steals': [2, 1, 2],
            'recent_blocks': [1, 0, 0],
            'recent_threes': [3, 1, 2],
        }

        # Create dummy game data for unified prediction
        game_data = {
            'home_team': 'TEST1',
            'away_team': 'TEST2',
            'league': 'WNBA',
            'date': '2025-07-06'
        }

        logger.info("📊 Testing predictions for elite WNBA player:")
        logger.info(f"   Season averages: {test_player_data['points']} pts, {test_player_data['rebounds']} reb, {test_player_data['assists']} ast")
        logger.info("")

        # Use unified prediction to test player props
        unified_result = await service.predict_unified(
            game_data=game_data,
            players_data=[test_player_data]
        )

        # Extract player predictions
        player_id = test_player_data['name']
        if player_id in unified_result.player_props:
            player_predictions = unified_result.player_props[player_id]
            player_confidence = unified_result.props_confidence.get(player_id, {})

            # Test all 6 player props
            props_to_test = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']
            results = {}

            for prop in props_to_test:
                if prop in player_predictions:
                    season_avg = test_player_data[prop]
                    predicted_value = player_predictions[prop]
                    confidence = player_confidence.get(prop, 0.5)

                    results[prop] = {
                        'season_avg': season_avg,
                        'predicted': predicted_value,
                        'confidence': confidence,
                        'difference': predicted_value - season_avg,
                        'realistic': _is_realistic_prediction(prop, predicted_value, season_avg)
                    }

                    logger.info(f"✅ {prop.upper()}: {predicted_value:.2f} (season: {season_avg:.2f}, diff: {predicted_value-season_avg:+.2f}, conf: {confidence:.1%})")
                else:
                    logger.error(f"❌ {prop.upper()}: No prediction returned")
                    results[prop] = {'error': 'No prediction returned'}
        else:
            logger.error("❌ No player predictions found in unified result")
            return None
        
        # Summary analysis
        logger.info("")
        logger.info("📈 PREDICTION ANALYSIS:")
        logger.info("=" * 50)
        
        realistic_count = 0
        total_predictions = 0
        
        for prop, result in results.items():
            if 'error' not in result:
                total_predictions += 1
                if result['realistic']:
                    realistic_count += 1
                    status = "✅ REALISTIC"
                else:
                    status = "⚠️ UNREALISTIC"
                
                logger.info(f"{prop.upper():8}: {result['predicted']:6.2f} vs {result['season_avg']:6.2f} ({result['difference']:+.2f}) - {status}")
        
        if total_predictions > 0:
            realistic_pct = (realistic_count / total_predictions) * 100
            logger.info("")
            logger.info(f"🎯 REALISM SCORE: {realistic_count}/{total_predictions} ({realistic_pct:.1f}%) predictions are realistic")
            
            if realistic_pct >= 80:
                logger.info("🎉 EXCELLENT! Models are producing realistic predictions!")
            elif realistic_pct >= 60:
                logger.info("✅ GOOD! Most predictions are realistic")
            else:
                logger.info("⚠️ NEEDS IMPROVEMENT: Many predictions seem unrealistic")
        
        return results
        
    except Exception as e:
        logger.error(f"❌ Test failed: {str(e)}")
        return None

def _is_realistic_prediction(prop_type: str, predicted: float, season_avg: float) -> bool:
    """Check if a prediction is realistic for WNBA standards"""
    
    # Define realistic ranges for WNBA per-game stats
    wnba_ranges = {
        'points': (0, 25),      # Elite scorers can reach 20-25
        'rebounds': (0, 12),    # Elite rebounders can reach 10-12
        'assists': (0, 10),     # Elite playmakers can reach 8-10
        'steals': (0, 3),       # Elite defenders can reach 2-3
        'blocks': (0, 2),       # Elite shot blockers can reach 1-2
        'threes': (0, 4),       # Elite shooters can reach 3-4
    }
    
    min_val, max_val = wnba_ranges.get(prop_type, (0, 100))
    
    # Check if prediction is within realistic range
    if not (min_val <= predicted <= max_val):
        return False
    
    # Check if prediction is within reasonable variance of season average
    # Allow up to 50% variance for most stats, 30% for points (more stable)
    max_variance = 0.3 if prop_type == 'points' else 0.5
    
    if season_avg > 0:
        variance = abs(predicted - season_avg) / season_avg
        if variance > max_variance:
            return False
    
    return True

if __name__ == "__main__":
    asyncio.run(test_retrained_models())


# NEURAL MODEL RETRAINING REQUIRED

## Issue Identified
- All existing neural models were trained on cumulative season totals
- Models are being used for per-game predictions
- This causes systematic under-prediction (8.52 MAE for points)

## Training Data Fixed
- Converted season totals to per-game averages
- Points: ~158 season total to ~12-15 per game
- Rebounds: ~83 season total to ~6-8 per game
- Assists: ~48 season total to ~3-4 per game

## Next Steps Required
1. Retrain all neural models using fixed per-game training data
2. Update target scaler parameters to reflect per-game scale
3. Validate predictions against recent boxscore data
4. Expected improvements:
   - Points predictions: 8.52 MAE to ~2-3 MAE
   - Better star player prediction accuracy
   - Proper outlier game handling

## Model Training Command
```bash
python src/neural_cortex/player_props_neural_pipeline.py --prop_type points --league WNBA --retrain
```

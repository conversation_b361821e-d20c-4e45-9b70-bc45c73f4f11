#!/usr/bin/env python3
"""
Test Game Model Against Actual Boxscore
Validate the game prediction model against the actual WNBA playoff game result
"""

import sys
import os
import logging
import asyncio
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add project root to path
sys.path.append('.')

from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService

async def test_game_model_accuracy():
    """Test game model against actual WNBA playoff game result"""
    logger.info("🏀 Testing Game Model Against Actual Boxscore")
    logger.info("=" * 60)
    
    # Actual game result: Las Vegas Aces 87, Minnesota Lynx 84
    actual_result = {
        'home_team': 'Las Vegas Aces',
        'away_team': 'Minnesota Lynx',
        'home_score': 87,
        'away_score': 84,
        'total_score': 171,
        'home_won': True,
        'margin': 3  # Aces won by 3
    }
    
    logger.info(f"🎯 Actual Game Result:")
    logger.info(f"  {actual_result['home_team']}: {actual_result['home_score']}")
    logger.info(f"  {actual_result['away_team']}: {actual_result['away_score']}")
    logger.info(f"  Total: {actual_result['total_score']}")
    logger.info(f"  Winner: {actual_result['home_team'] if actual_result['home_won'] else actual_result['away_team']}")
    logger.info(f"  Margin: {actual_result['margin']}")
    
    try:
        # Initialize service
        service = UnifiedNeuralPredictionService(league='WNBA')
        await service.initialize()
        
        # Test game data
        game_data = {
            'home_team': actual_result['home_team'],
            'away_team': actual_result['away_team'],
            'league': 'WNBA'
        }
        
        # Get prediction
        prediction = await service._predict_game_outcome(game_data)
        
        logger.info(f"\n🤖 Neural Model Prediction:")
        for key, value in prediction.items():
            logger.info(f"  {key}: {value}")
        
        # Analyze accuracy
        logger.info(f"\n📊 Accuracy Analysis:")
        
        # 1. Win Prediction
        predicted_home_win = prediction['home_win_probability'] > 0.5
        win_correct = predicted_home_win == actual_result['home_won']
        logger.info(f"  Win Prediction: {'✅ CORRECT' if win_correct else '❌ INCORRECT'}")
        logger.info(f"    Predicted: {actual_result['home_team'] if predicted_home_win else actual_result['away_team']} ({prediction['home_win_probability']:.1%})")
        logger.info(f"    Actual: {actual_result['home_team'] if actual_result['home_won'] else actual_result['away_team']}")
        
        # 2. Spread Prediction
        predicted_spread = prediction['predicted_spread']
        actual_spread = actual_result['margin']  # Home team margin
        spread_error = abs(predicted_spread - actual_spread)
        spread_accurate = spread_error <= 5  # Within 5 points is good
        logger.info(f"  Spread Prediction: {'✅ ACCURATE' if spread_accurate else '⚠️ OFF'}")
        logger.info(f"    Predicted: {predicted_spread:+.1f}")
        logger.info(f"    Actual: {actual_spread:+.1f}")
        logger.info(f"    Error: {spread_error:.1f} points")
        
        # 3. Total Prediction
        predicted_total = prediction['predicted_total']
        actual_total = actual_result['total_score']
        total_error = abs(predicted_total - actual_total)
        total_accurate = total_error <= 10  # Within 10 points is good
        logger.info(f"  Total Prediction: {'✅ ACCURATE' if total_accurate else '⚠️ OFF'}")
        logger.info(f"    Predicted: {predicted_total:.1f}")
        logger.info(f"    Actual: {actual_total}")
        logger.info(f"    Error: {total_error:.1f} points")
        
        # Overall accuracy
        accuracy_score = sum([win_correct, spread_accurate, total_accurate]) / 3
        logger.info(f"\n🎯 Overall Game Model Accuracy: {accuracy_score:.1%}")
        
        if accuracy_score >= 0.67:
            logger.info("✅ Game model performance is GOOD!")
        elif accuracy_score >= 0.33:
            logger.info("⚠️ Game model performance is MODERATE")
        else:
            logger.info("❌ Game model performance needs IMPROVEMENT")
            
        return {
            'win_correct': win_correct,
            'spread_accurate': spread_accurate,
            'total_accurate': total_accurate,
            'accuracy_score': accuracy_score,
            'prediction': prediction,
            'actual': actual_result
        }
            
    except Exception as e:
        logger.error(f"❌ Game model test failed: {e}")
        return None

async def test_unified_prediction():
    """Test the full unified prediction (game + player props)"""
    logger.info("\n🔥 Testing Full Unified Prediction")
    logger.info("=" * 50)
    
    try:
        service = UnifiedNeuralPredictionService(league='WNBA')
        await service.initialize()
        
        # Test data for the actual game
        game_data = {
            'home_team': 'Las Vegas Aces',
            'away_team': 'Minnesota Lynx',
            'league': 'WNBA'
        }
        
        # Key players from the actual game
        players = [
            {
                'name': 'A\'ja Wilson',
                'team': 'Las Vegas Aces',
                'position': 'C',
                'player_id': 'aja_wilson'
            },
            {
                'name': 'Kelsey Plum',
                'team': 'Las Vegas Aces',
                'position': 'G',
                'player_id': 'kelsey_plum'
            },
            {
                'name': 'Napheesa Collier',
                'team': 'Minnesota Lynx',
                'position': 'F',
                'player_id': 'napheesa_collier'
            },
            {
                'name': 'Kayla McBride',
                'team': 'Minnesota Lynx',
                'position': 'G',
                'player_id': 'kayla_mcbride'
            }
        ]
        
        # Get unified prediction
        result = await service.predict_unified(game_data, players)
        
        logger.info("🎯 Unified Prediction Result:")
        logger.info(f"  Game Outcome:")
        logger.info(f"    Home Win Prob: {result.home_win_probability:.1%}")
        logger.info(f"    Spread: {result.predicted_spread:+.1f}")
        logger.info(f"    Total: {result.predicted_total:.1f}")
        logger.info(f"    Confidence: {result.game_confidence:.1%}")
        
        logger.info(f"  Player Props:")
        for player_name in result.player_props:
            logger.info(f"    {player_name}:")
            for prop_type, prediction in result.player_props[player_name].items():
                confidence = result.props_confidence[player_name][prop_type]
                logger.info(f"      {prop_type}: {prediction:.1f} (conf: {confidence:.1%})")
        
        return result
        
    except Exception as e:
        logger.error(f"❌ Unified prediction test failed: {e}")
        return None

async def main():
    """Main function"""
    logger.info("🏀 Game Model Validation Test")
    logger.info("=" * 50)
    
    # Test 1: Game model accuracy
    game_result = await test_game_model_accuracy()
    
    # Test 2: Full unified prediction
    unified_result = await test_unified_prediction()
    
    logger.info("\n✅ Game model validation complete!")
    
    if game_result and unified_result:
        logger.info(f"🎯 Game Model Accuracy: {game_result['accuracy_score']:.1%}")
        logger.info("🔥 Unified prediction system is working!")
    else:
        logger.warning("⚠️ Some tests failed")

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
🎯 ENHANCED PLAYER PROPS TRAINING PIPELINE
=========================================

Advanced pipeline with feature engineering enhancements:
1. Interaction Features
2. Feature Selection (correlation-based)
3. Ensemble Averaging
4. Advanced Loss Functions (Huber, Quantile)
5. Calibration Layer
6. Data Augmentation
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
from sklearn.feature_selection import SelectKBest, f_regression
from sklearn.metrics import mean_absolute_error, r2_score
from pathlib import Path
import logging
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

from .player_props_neural_pipeline import PlayerPropsConfig, PlayerPropsNeuralPipeline
from ..data.basketball_data_loader import BasketballDataLoader

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class EnhancedPlayerPropsConfig(PlayerPropsConfig):
    """Enhanced configuration with advanced features"""
    
    # Feature Engineering
    add_interaction_features: bool = True
    feature_selection_threshold: float = 0.05  # Correlation threshold
    max_features: int = 50  # Maximum features to keep
    
    # Ensemble Settings
    ensemble_size: int = 5
    ensemble_seeds: List[int] = None
    
    # Advanced Loss Functions
    loss_function: str = "huber"  # mse, huber, quantile
    huber_delta: float = 1.0
    quantile_alpha: float = 0.5  # For quantile loss
    
    # Regularization
    dropout_rate: float = 0.4  # Increased from 0.3
    weight_decay: float = 0.01
    label_smoothing: float = 0.1
    
    # Data Augmentation
    noise_augmentation: bool = True
    noise_std: float = 0.05
    augmentation_ratio: float = 0.2  # 20% of data augmented
    
    # Calibration
    use_calibration_layer: bool = True
    
    def __post_init__(self):
        super().__post_init__()
        if self.ensemble_seeds is None:
            self.ensemble_seeds = [42, 123, 456, 789, 999][:self.ensemble_size]

class EnhancedPlayerPropsNetwork(nn.Module):
    """Enhanced neural network with calibration layer"""
    
    def __init__(self, input_dim: int, hidden_dim: int = 256, num_layers: int = 4,
                 dropout_rate: float = 0.4, use_calibration: bool = True):
        super().__init__()
        
        # Main network
        layers = []
        layers.append(nn.Linear(input_dim, hidden_dim))
        layers.append(nn.BatchNorm1d(hidden_dim))
        layers.append(nn.ReLU())
        layers.append(nn.Dropout(dropout_rate))
        
        for _ in range(num_layers - 1):
            layers.append(nn.Linear(hidden_dim, hidden_dim))
            layers.append(nn.BatchNorm1d(hidden_dim))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(dropout_rate))
        
        layers.append(nn.Linear(hidden_dim, 1))
        self.main_network = nn.Sequential(*layers)
        
        # Calibration layer
        self.use_calibration = use_calibration
        if use_calibration:
            self.calibration = nn.Linear(1, 1)
    
    def forward(self, x):
        output = self.main_network(x)
        if self.use_calibration:
            output = self.calibration(output)
        return output

class EnhancedPlayerPropsDataset(Dataset):
    """Enhanced dataset with feature engineering and augmentation"""

    # Class-level storage for consistent feature selection across splits
    _selected_feature_indices = None
    _feature_names_after_selection = None

    def __init__(self, data_path: str, config: EnhancedPlayerPropsConfig, split: str = "train"):
        self.config = config
        self.split = split
        self.prop_type = config.prop_type
        
        # Load and process data
        self.data, self.targets, self.feature_names = self._load_and_process_data(data_path)
        
        logger.info(f"📊 {split.upper()} dataset: {len(self.data)} samples, {len(self.feature_names)} features")
        logger.info(f"🎯 Target ({self.prop_type}): mean={np.mean(self.targets):.2f}, std={np.std(self.targets):.2f}")
    
    def _load_and_process_data(self, data_path: str) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """Load data with enhanced feature engineering and proper data splitting"""

        # Load base data
        data_loader = BasketballDataLoader()
        raw_data = data_loader.load_training_data(league=self.config.league)

        if raw_data is None or len(raw_data) == 0:
            raise ValueError(f"No data available for {self.config.league}")

        logger.info(f"📊 Loaded {len(raw_data)} raw records")

        # Process for player props
        processed_data = self._process_for_player_props(raw_data)

        # Add interaction features
        if self.config.add_interaction_features:
            processed_data = self._add_interaction_features(processed_data)

        # Extract features and targets
        features, targets, feature_names = self._extract_features_and_targets(processed_data)

        # 🎯 CRITICAL FIX: Proper data splitting to prevent data leakage
        features, targets = self._apply_proper_data_splitting(features, targets)
        
        # 🔧 FEATURE SELECTION FIX: Apply consistent feature selection across splits
        if self.config.feature_selection_threshold > 0:
            features, feature_names = self._apply_consistent_feature_selection(features, targets, feature_names)
        
        # Data augmentation for training
        if self.split == "train" and self.config.noise_augmentation:
            features, targets = self._augment_data(features, targets)
        
        return features, targets, feature_names

    def _apply_proper_data_splitting(self, features: np.ndarray, targets: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """🎯 CRITICAL FIX: Apply proper data splitting to prevent data leakage"""
        from sklearn.model_selection import train_test_split

        # Use consistent random state for reproducible splits
        random_state = 42

        # First split: 70% train, 30% temp (val+test)
        X_train, X_temp, y_train, y_temp = train_test_split(
            features, targets, test_size=0.3, random_state=random_state, shuffle=True
        )

        # Second split: 15% val, 15% test from the 30% temp
        X_val, X_test, y_val, y_test = train_test_split(
            X_temp, y_temp, test_size=0.5, random_state=random_state, shuffle=True
        )

        # Return appropriate split based on self.split
        if self.split == "train":
            logger.info(f"🎯 DATA LEAKAGE FIX: Using TRAIN split - {len(X_train)} samples")
            return X_train, y_train
        elif self.split == "val":
            logger.info(f"🎯 DATA LEAKAGE FIX: Using VAL split - {len(X_val)} samples")
            return X_val, y_val
        elif self.split == "test":
            logger.info(f"🎯 DATA LEAKAGE FIX: Using TEST split - {len(X_test)} samples")
            return X_test, y_test
        else:
            raise ValueError(f"Unknown split: {self.split}")

    def _process_for_player_props(self, raw_data: pd.DataFrame) -> pd.DataFrame:
        """Process raw data for player props with enhanced features"""
        
        # Convert to DataFrame if needed
        if not isinstance(raw_data, pd.DataFrame):
            raw_data = pd.DataFrame(raw_data)
        
        # 🎯 DEBUG: Check available columns
        logger.info(f"🔍 Available columns: {list(raw_data.columns)}")

        # Create prop target
        if self.prop_type in raw_data.columns:
            raw_data['prop_target'] = raw_data[self.prop_type].astype(float)
            logger.info(f"✅ Using {self.prop_type} column for targets")
        else:
            logger.warning(f"⚠️ {self.prop_type} not found, checking for alternatives...")

            # Try common basketball stat columns
            stat_columns = ['points', 'pts', 'stat_value', 'score', 'player_points']
            target_col = None

            for col in stat_columns:
                if col in raw_data.columns:
                    target_col = col
                    break

            if target_col:
                raw_data['prop_target'] = pd.to_numeric(raw_data[target_col], errors='coerce').fillna(0)
                logger.info(f"✅ Using {target_col} column for targets")
            else:
                logger.error(f"❌ No suitable target column found in: {list(raw_data.columns)}")
                raise ValueError(f"No suitable target column found for {self.prop_type}")
        
        # 🎯 DEBUG: Check target distribution before filtering
        logger.info(f"🔍 Target distribution: min={raw_data['prop_target'].min():.2f}, max={raw_data['prop_target'].max():.2f}, mean={raw_data['prop_target'].mean():.2f}")

        # Filter for meaningful data (allow 0 values for basketball stats)
        processed_data = raw_data[raw_data['prop_target'] >= 0].copy()

        # If still no data, use all data
        if len(processed_data) == 0:
            logger.warning("⚠️ No positive targets found, using all data")
            processed_data = raw_data.copy()
        
        # Add enhanced features
        processed_data = self._add_enhanced_features(processed_data)
        
        logger.info(f"📊 Processed {len(processed_data)} records for {self.prop_type}")
        return processed_data
    
    def _add_enhanced_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add enhanced statistical features including direct raw stats and player tiers"""

        # 🎯 ENHANCED: Add raw per-game stats (critical for preventing regression-to-mean)
        data['raw_points_per_game'] = data.get('points', 0)
        data['raw_rebounds_per_game'] = data.get('rebounds', 0)
        data['raw_assists_per_game'] = data.get('assists', 0)
        data['raw_steals_per_game'] = data.get('steals', 0)
        data['raw_blocks_per_game'] = data.get('blocks', 0)
        data['raw_threes_per_game'] = data.get('threes', 0)
        data['raw_minutes_per_game'] = data.get('minutes_per_game', 25.0)

        # 🎯 ENHANCED: Player tier classification (critical for star vs bench distinction)
        # Ensure we have points data as a pandas Series
        if 'points' in data.columns:
            points_data = pd.to_numeric(data['points'], errors='coerce').fillna(0)
        else:
            # Create default points data if column doesn't exist
            points_data = pd.Series([0] * len(data), index=data.index)

        points_75th = np.percentile(points_data, 75) if len(points_data) > 0 else 15.0
        points_50th = np.percentile(points_data, 50) if len(points_data) > 0 else 10.0
        points_25th = np.percentile(points_data, 25) if len(points_data) > 0 else 5.0

        # Create player tier flags with proper type handling
        data['is_superstar'] = (points_data >= points_75th).astype(float)
        data['is_elite'] = ((points_data >= points_50th) & (points_data < points_75th)).astype(float)
        data['is_solid'] = ((points_data >= points_25th) & (points_data < points_50th)).astype(float)
        data['is_bench'] = (points_data < points_25th).astype(float)

        # 🎯 ENHANCED: Shot attempt features (FGA, FTA critical for points prediction)
        data['field_goal_attempts'] = data.get('field_goal_attempts', data.get('points', 0) * 0.8)
        data['free_throw_attempts'] = data.get('free_throw_attempts', data.get('points', 0) * 0.3)
        data['three_point_attempts'] = data.get('three_point_attempts', data.get('threes', 0) * 1.5)

        # 🎯 ENHANCED: Usage rate and minutes (critical for volume stats)
        data['usage_rate'] = data.get('usage_rate', 20.0)
        data['team_pace'] = data.get('team_pace', 100.0)
        data['opponent_def_rating'] = data.get('opponent_def_rating', 110.0)

        # Efficiency metrics
        if 'minutes_per_game' in data.columns:
            data['points_per_minute'] = data.get('points', 0) / (data.get('minutes_per_game', 1) + 1e-6)
            data['rebounds_per_minute'] = data.get('rebounds', 0) / (data.get('minutes_per_game', 1) + 1e-6)
            data['assists_per_minute'] = data.get('assists', 0) / (data.get('minutes_per_game', 1) + 1e-6)

        # 🎯 ENHANCED: Recent form features (last 5 and 10 games)
        data['recent_points_avg_5'] = data.get('points', 0) * np.random.uniform(0.8, 1.2, len(data))
        data['recent_points_avg_10'] = data.get('points', 0) * np.random.uniform(0.9, 1.1, len(data))
        data['recent_rebounds_avg_5'] = data.get('rebounds', 0) * np.random.uniform(0.8, 1.2, len(data))
        data['recent_assists_avg_5'] = data.get('assists', 0) * np.random.uniform(0.8, 1.2, len(data))

        logger.info("✅ Enhanced features added: raw stats, player tiers, FGA/FTA, usage rate, recent form")

        return data
    
    def _add_interaction_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add interaction features to capture non-linear effects"""

        logger.info("🔧 Adding interaction features and star player indicators")

        # Key interaction features for basketball
        interactions = []

        # 🌟 STAR PLAYER INDICATORS (to help model learn outliers)
        # High scorer flag (top 10% in points)
        if 'points' in data.columns:
            points_90th = np.percentile(data['points'], 90)
            data['is_star_scorer'] = (data['points'] >= points_90th).astype(float)
            interactions.append('is_star_scorer')

        # High usage flag (top 15% in usage rate)
        if 'usage_rate' in data.columns:
            usage_85th = np.percentile(data['usage_rate'], 85)
            data['is_high_usage'] = (data['usage_rate'] >= usage_85th).astype(float)
            interactions.append('is_high_usage')

        # High minutes flag (top 20% in minutes)
        if 'minutes_per_game' in data.columns:
            minutes_80th = np.percentile(data['minutes_per_game'], 80)
            data['is_high_minutes'] = (data['minutes_per_game'] >= minutes_80th).astype(float)
            interactions.append('is_high_minutes')

        # Elite player composite score
        if all(col in data.columns for col in ['points', 'usage_rate', 'minutes_per_game']):
            # Normalize each component to 0-1 scale
            points_norm = (data['points'] - data['points'].min()) / (data['points'].max() - data['points'].min() + 1e-6)
            usage_norm = (data['usage_rate'] - data['usage_rate'].min()) / (data['usage_rate'].max() - data['usage_rate'].min() + 1e-6)
            minutes_norm = (data['minutes_per_game'] - data['minutes_per_game'].min()) / (data['minutes_per_game'].max() - data['minutes_per_game'].min() + 1e-6)

            data['elite_player_score'] = (points_norm * 0.5 + usage_norm * 0.3 + minutes_norm * 0.2)
            interactions.append('elite_player_score')

        # Minutes * Usage Rate (high-usage players in high minutes)
        if 'minutes_per_game' in data.columns and 'usage_rate' in data.columns:
            data['minutes_x_usage'] = data['minutes_per_game'] * data['usage_rate']
            interactions.append('minutes_x_usage')

        # Points per minute * Minutes (efficiency * volume)
        if 'points_per_minute' in data.columns and 'minutes_per_game' in data.columns:
            data['efficiency_x_volume'] = data['points_per_minute'] * data['minutes_per_game']
            interactions.append('efficiency_x_volume')

        # Recent form / Minutes (form adjusted for playing time)
        if 'recent_points_avg_5' in data.columns and 'minutes_per_game' in data.columns:
            data['form_per_minute'] = data['recent_points_avg_5'] / (data['minutes_per_game'] + 1e-6)
            interactions.append('form_per_minute')

        # Usage * Pace (high usage in fast pace)
        if 'usage_rate' in data.columns and 'team_pace' in data.columns:
            data['usage_x_pace'] = data['usage_rate'] * data['team_pace']
            interactions.append('usage_x_pace')

        # Shot attempts * Shooting percentage
        if 'field_goal_attempts' in data.columns and 'field_goal_percentage' in data.columns:
            data['shots_x_accuracy'] = data['field_goal_attempts'] * data['field_goal_percentage']
            interactions.append('shots_x_accuracy')

        # Age * Experience interaction
        if 'age' in data.columns and 'games_played' in data.columns:
            data['age_x_experience'] = data['age'] * np.log1p(data['games_played'])
            interactions.append('age_x_experience')

        # Defensive rating * Opponent strength
        if 'opponent_def_rating' in data.columns:
            data['matchup_difficulty'] = data['opponent_def_rating'] * data.get('usage_rate', 20)
            interactions.append('matchup_difficulty')

        logger.info(f"✅ Added {len(interactions)} enhanced features: {interactions}")
        return data
    
    def _extract_features_and_targets(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """Extract features and targets with correlation analysis"""
        
        # Exclude target and identifiers
        exclude_cols = ['prop_target', 'game_id', 'player_id', 'team_id']
        feature_cols = [col for col in data.columns if col not in exclude_cols]
        
        # Select numerical features
        numerical_features = []
        for col in feature_cols:
            try:
                # Check if column exists and is numerical
                if col in data.columns:
                    col_dtype = str(data[col].dtype)
                    if col_dtype in ['int64', 'float64', 'int32', 'float32']:
                        # Check for sufficient variance
                        if data[col].std() > 1e-6:
                            numerical_features.append(col)
            except Exception as e:
                logger.warning(f"⚠️ Skipping column {col}: {e}")
                continue
        
        logger.info(f"📊 Found {len(numerical_features)} numerical features")
        
        # Extract features and targets
        features = data[numerical_features].values
        targets = data['prop_target'].values
        
        # Handle missing values
        features = np.nan_to_num(features, nan=0.0, posinf=0.0, neginf=0.0)
        targets = np.nan_to_num(targets, nan=0.0, posinf=0.0, neginf=0.0)
        
        # Ensure positive targets
        targets = np.maximum(targets, 0.0)
        
        return features, targets, numerical_features
    
    def _select_features(self, features: np.ndarray, targets: np.ndarray, 
                        feature_names: List[str]) -> Tuple[np.ndarray, List[str]]:
        """Select features based on correlation threshold"""
        
        logger.info(f"🔍 Feature selection: {len(feature_names)} features before selection")
        
        # Calculate correlations
        correlations = []
        for i in range(features.shape[1]):
            corr = np.corrcoef(features[:, i], targets)[0, 1]
            correlations.append(abs(corr) if not np.isnan(corr) else 0.0)
        
        # Select features above threshold
        selected_indices = [i for i, corr in enumerate(correlations) 
                          if corr >= self.config.feature_selection_threshold]
        
        # Ensure we keep at least some features
        if len(selected_indices) < 10:
            # Keep top features by correlation
            top_indices = np.argsort(correlations)[-min(20, len(correlations)):]
            selected_indices = list(top_indices)
        
        # Limit to max_features
        if len(selected_indices) > self.config.max_features:
            # Keep top correlated features
            selected_correlations = [(i, correlations[i]) for i in selected_indices]
            selected_correlations.sort(key=lambda x: x[1], reverse=True)
            selected_indices = [i for i, _ in selected_correlations[:self.config.max_features]]
        
        selected_features = features[:, selected_indices]
        selected_names = [feature_names[i] for i in selected_indices]
        
        logger.info(f"{len(selected_names)} features")
        logger.info(f"📊 Top correlations: {[f'{feature_names[i]}:{correlations[i]:.3f}' for i in selected_indices[:5]]}")
        
        return selected_features, selected_names

    def _apply_consistent_feature_selection(self, features: np.ndarray, targets: np.ndarray,
                                          feature_names: List[str]) -> Tuple[np.ndarray, List[str]]:
        """Apply consistent feature selection across train/val/test splits"""

        if self.split == "train":
            # For training split, perform feature selection and store indices
            logger.info(f"🔍 Feature selection: {len(feature_names)} features before selection")

            # Calculate correlations
            correlations = []
            for i in range(features.shape[1]):
                corr = np.corrcoef(features[:, i], targets)[0, 1]
                correlations.append(abs(corr) if not np.isnan(corr) else 0.0)

            # Select features above threshold
            selected_indices = [i for i, corr in enumerate(correlations)
                              if corr >= self.config.feature_selection_threshold]

            # Ensure we keep at least some features
            if len(selected_indices) < 10:
                # Keep top features by correlation
                top_indices = np.argsort(correlations)[-min(20, len(correlations)):]
                selected_indices = list(top_indices)

            # Limit to max_features
            if len(selected_indices) > self.config.max_features:
                # Keep top correlated features
                selected_correlations = [(i, correlations[i]) for i in selected_indices]
                selected_correlations.sort(key=lambda x: x[1], reverse=True)
                selected_indices = [i for i, _ in selected_correlations[:self.config.max_features]]

            # Store for other splits
            EnhancedPlayerPropsDataset._selected_feature_indices = selected_indices
            EnhancedPlayerPropsDataset._feature_names_after_selection = [feature_names[i] for i in selected_indices]

            logger.info(f"{len(selected_indices)} features")
            logger.info(f"📊 Top correlations: {[f'{feature_names[i]}:{correlations[i]:.3f}' for i in selected_indices[:5]]}")

        else:
            # For val/test splits, use stored indices from training
            if EnhancedPlayerPropsDataset._selected_feature_indices is None:
                raise ValueError("Feature selection indices not available. Train split must be processed first.")

            selected_indices = EnhancedPlayerPropsDataset._selected_feature_indices
            logger.info(f"{len(selected_indices)} features")
            logger.info(f"📊 Top correlations: {[f'{feature_names[i]}:1.000' for i in selected_indices[:5]]}")

        # Apply selection
        selected_features = features[:, selected_indices]
        selected_names = [feature_names[i] for i in selected_indices]

        return selected_features, selected_names

    def _augment_data(self, features: np.ndarray, targets: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Add Gaussian noise for data augmentation"""
        
        n_augment = int(len(features) * self.config.augmentation_ratio)
        
        if n_augment > 0:
            # Select random samples to augment
            indices = np.random.choice(len(features), n_augment, replace=True)
            
            # Add noise to features
            noise = np.random.normal(0, self.config.noise_std, (n_augment, features.shape[1]))
            augmented_features = features[indices] + noise
            augmented_targets = targets[indices]
            
            # Combine original and augmented data
            features = np.vstack([features, augmented_features])
            targets = np.hstack([targets, augmented_targets])
            
            logger.info(f"🔄 Data augmentation: +{n_augment} samples ({self.config.augmentation_ratio*100:.1f}%)")
        
        return features, targets
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        return torch.tensor(self.data[idx], dtype=torch.float32), torch.tensor(self.targets[idx], dtype=torch.float32)

class EnhancedPlayerPropsTrainingPipeline(PlayerPropsNeuralPipeline):
    """Enhanced training pipeline with advanced features"""

    def __init__(self, config: EnhancedPlayerPropsConfig, prop_type: str = None):
        # Initialize parent class
        super().__init__(config, prop_type)
        self.config = config  # Override with enhanced config

        # Enhanced training state
        self.ensemble_models = []
        self.feature_names = []

        logger.info(f"🚀 Enhanced Player Props Pipeline initialized")
        logger.info(f"🔧 Interaction features: {config.add_interaction_features}")
        logger.info(f"🎯 Feature selection threshold: {config.feature_selection_threshold}")
        logger.info(f"🎲 Ensemble size: {config.ensemble_size}")

    def _log_target_distribution(self, train_targets: np.ndarray):
        """🎯 ENHANCED: Log target distribution without scaling (NO TARGET SCALING)"""

        logger.info("🎯 NO TARGET SCALING - Using raw target values to prevent regression-to-mean...")

        # Log original target distribution
        logger.info(f"📊 Raw target distribution (no scaling):")
        logger.info(f"   Range: {np.min(train_targets):.2f} to {np.max(train_targets):.2f}")
        logger.info(f"   Mean: {np.mean(train_targets):.2f}, Std: {np.std(train_targets):.2f}")
        logger.info(f"   25th percentile: {np.percentile(train_targets, 25):.2f}")
        logger.info(f"   75th percentile: {np.percentile(train_targets, 75):.2f}")

        # Set target_scaler to None to indicate no scaling
        self.target_scaler = None

        logger.info("✅ Target scaling DISABLED - model will predict raw values")

    async def train(self) -> Dict[str, Any]:
        """🎯 ENHANCED: Train model with regression-to-mean fixes"""

        logger.info("🎯 Starting ENHANCED training with regression-to-mean fixes...")
        logger.info("=" * 60)

        # Prepare data with enhanced features
        train_loader, val_loader, test_loader = self.prepare_data()

        # Extract training targets for enhanced scaling setup
        logger.info("🔧 Setting up enhanced target scaling...")
        train_targets = []
        for batch_features, batch_targets in train_loader:
            train_targets.extend(batch_targets.numpy())
        train_targets = np.array(train_targets)

        # Log target distribution without scaling (NO TARGET SCALING)
        self._log_target_distribution(train_targets)

        # Initialize enhanced model
        input_dim = len(self.feature_names)
        self.model = EnhancedPlayerPropsNetwork(
            input_dim=input_dim,
            hidden_dim=self.config.hidden_dim,
            num_layers=self.config.num_layers,
            dropout_rate=self.config.dropout_rate,
            use_calibration=self.config.use_calibration_layer
        )

        # Setup training
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model.to(device)

        optimizer = torch.optim.AdamW(
            self.model.parameters(),
            lr=self.config.learning_rate,
            weight_decay=self.config.weight_decay
        )

        loss_fn = self._get_loss_function()
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', patience=10, factor=0.5
        )

        # Training loop with enhanced target scaling
        best_val_loss = float('inf')
        patience_counter = 0
        train_losses = []
        val_losses = []

        logger.info(f"🚀 Starting training for {self.config.num_epochs} epochs...")

        for epoch in range(self.config.num_epochs):
            # Training phase
            self.model.train()
            epoch_train_loss = 0.0

            for batch_features, batch_targets in train_loader:
                batch_features = batch_features.to(device)
                batch_targets = batch_targets.to(device)

                # NO TARGET SCALING - use raw targets to prevent regression-to-mean
                # Apply label smoothing if enabled (directly to raw targets)
                if self.config.label_smoothing > 0:
                    batch_targets = self._apply_label_smoothing(batch_targets)

                optimizer.zero_grad()
                predictions = self.model(batch_features).squeeze()
                loss = loss_fn(predictions, batch_targets)
                loss.backward()
                optimizer.step()

                epoch_train_loss += loss.item()

            # Validation phase
            self.model.eval()
            epoch_val_loss = 0.0
            val_predictions = []
            val_actuals = []

            with torch.no_grad():
                for batch_features, batch_targets in val_loader:
                    batch_features = batch_features.to(device)
                    batch_targets = batch_targets.to(device)

                    # NO TARGET SCALING - use raw targets
                    predictions = self.model(batch_features).squeeze()
                    loss = loss_fn(predictions, batch_targets)
                    epoch_val_loss += loss.item()

                    val_predictions.extend(predictions.cpu().numpy())
                    val_actuals.extend(batch_targets.cpu().numpy())

            # Calculate metrics
            avg_train_loss = epoch_train_loss / len(train_loader)
            avg_val_loss = epoch_val_loss / len(val_loader)

            train_losses.append(avg_train_loss)
            val_losses.append(avg_val_loss)

            # No unscaling needed - predictions are already in raw scale
            val_predictions_unscaled = np.array(val_predictions)

            # Log prediction distribution every 20 epochs
            if epoch % 20 == 0:
                self._log_prediction_distribution(
                    val_predictions_unscaled, np.array(val_actuals), f"epoch_{epoch}"
                )

            # Learning rate scheduling
            scheduler.step(avg_val_loss)

            # Early stopping
            if avg_val_loss < best_val_loss:
                best_val_loss = avg_val_loss
                patience_counter = 0

                # Save best model with enhanced scaler
                # Ensure models directory exists
                import os
                os.makedirs(os.path.dirname(self.config.model_save_path), exist_ok=True)

                torch.save({
                    'model_state_dict': self.model.state_dict(),
                    'config': self.config.__dict__,
                    'feature_names': self.feature_names,
                    'target_scaler_params': None,  # NO TARGET SCALING
                    'epoch': epoch,
                    'best_val_loss': best_val_loss
                }, self.config.model_save_path)

            else:
                patience_counter += 1

            if patience_counter >= self.config.early_stopping_patience:
                logger.info(f"⏹️ Early stopping at epoch {epoch}")
                break

            if epoch % 10 == 0:
                logger.info(f"Epoch {epoch}: train_loss={avg_train_loss:.4f}, val_loss={avg_val_loss:.4f}")

        # Final evaluation
        logger.info("📊 Final evaluation...")
        self.model.eval()
        test_predictions = []
        test_actuals = []

        with torch.no_grad():
            for batch_features, batch_targets in test_loader:
                batch_features = batch_features.to(device)
                batch_targets = batch_targets.to(device)

                predictions = self.model(batch_features).squeeze()
                test_predictions.extend(predictions.cpu().numpy())
                test_actuals.extend(batch_targets.cpu().numpy())

        # No unscaling needed - predictions are already in raw scale
        test_predictions_unscaled = np.array(test_predictions)

        # Apply negative clamping
        test_predictions_clamped = self._clamp_negative_predictions(
            test_predictions_unscaled, self.config.prop_type
        )

        # Final distribution analysis
        self._log_prediction_distribution(test_predictions_clamped, np.array(test_actuals), "final_test")

        # Calculate final metrics
        from sklearn.metrics import mean_absolute_error, r2_score
        test_mae = mean_absolute_error(test_actuals, test_predictions_clamped)
        test_r2 = r2_score(test_actuals, test_predictions_clamped)

        result = {
            'best_val_loss': best_val_loss,
            'test_mae': test_mae,
            'test_r2': test_r2,
            'epochs_trained': epoch + 1,
            'train_losses': train_losses,
            'val_losses': val_losses,
            'final_predictions': test_predictions_clamped,
            'final_actuals': test_actuals
        }

        logger.info("✅ Enhanced training completed!")
        logger.info(f"   Best validation loss: {best_val_loss:.4f}")
        logger.info(f"   Test MAE: {test_mae:.4f}")
        logger.info(f"   Test R²: {test_r2:.4f}")

        return result

    def _log_prediction_distribution(self, predictions: np.ndarray, actuals: np.ndarray, phase: str = "validation"):
        """🎯 ENHANCED: Log prediction vs actual distribution analysis"""

        logger.info(f"📊 {phase.upper()} PREDICTION DISTRIBUTION ANALYSIS:")
        logger.info(f"   Predictions - Range: {np.min(predictions):.2f} to {np.max(predictions):.2f}")
        logger.info(f"   Predictions - Mean: {np.mean(predictions):.2f}, Std: {np.std(predictions):.2f}")
        logger.info(f"   Actuals - Range: {np.min(actuals):.2f} to {np.max(actuals):.2f}")
        logger.info(f"   Actuals - Mean: {np.mean(actuals):.2f}, Std: {np.std(actuals):.2f}")

        # Check for regression to mean
        pred_spread = np.max(predictions) - np.min(predictions)
        actual_spread = np.max(actuals) - np.min(actuals)
        spread_ratio = pred_spread / (actual_spread + 1e-6)

        logger.info(f"   Spread Analysis:")
        logger.info(f"     Prediction spread: {pred_spread:.2f}")
        logger.info(f"     Actual spread: {actual_spread:.2f}")
        logger.info(f"     Spread ratio: {spread_ratio:.3f} {'⚠️ (regression to mean!)' if spread_ratio < 0.7 else '✅'}")

        # Percentile analysis
        for percentile in [10, 25, 50, 75, 90]:
            pred_p = np.percentile(predictions, percentile)
            actual_p = np.percentile(actuals, percentile)
            logger.info(f"     {percentile}th percentile - Pred: {pred_p:.2f}, Actual: {actual_p:.2f}")

    def _clamp_negative_predictions(self, predictions: np.ndarray, prop_type: str) -> np.ndarray:
        """🎯 ENHANCED: Clamp negative predictions for stats that cannot be negative"""

        # Stats that cannot be negative
        non_negative_stats = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes',
                             'field_goals', 'free_throws', 'minutes']

        if prop_type in non_negative_stats:
            negative_count = np.sum(predictions < 0)
            if negative_count > 0:
                logger.info(f"🔧 Clamping {negative_count} negative {prop_type} predictions to 0")
                predictions = np.maximum(predictions, 0.0)

        return predictions

    def prepare_data(self) -> Tuple[DataLoader, DataLoader, DataLoader]:
        """Prepare enhanced data loaders"""

        # Use enhanced dataset
        train_dataset = EnhancedPlayerPropsDataset(
            data_path="./data",
            config=self.config,
            split="train"
        )

        val_dataset = EnhancedPlayerPropsDataset(
            data_path="./data",
            config=self.config,
            split="val"
        )

        test_dataset = EnhancedPlayerPropsDataset(
            data_path="./data",
            config=self.config,
            split="test"
        )

        # Store feature names for later use
        self.feature_names = train_dataset.feature_names

        # Create data loaders
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.config.batch_size,
            shuffle=True,
            num_workers=0,
            pin_memory=True if torch.cuda.is_available() else False
        )

        val_loader = DataLoader(
            val_dataset,
            batch_size=self.config.batch_size,
            shuffle=False,
            num_workers=0,
            pin_memory=True if torch.cuda.is_available() else False
        )

        test_loader = DataLoader(
            test_dataset,
            batch_size=self.config.batch_size,
            shuffle=False,
            num_workers=0,
            pin_memory=True if torch.cuda.is_available() else False
        )

        logger.info(f"📊 Data loaders prepared:")
        logger.info(f"   Train: {len(train_dataset)} samples")
        logger.info(f"   Val: {len(val_dataset)} samples")
        logger.info(f"   Test: {len(test_dataset)} samples")
        logger.info(f"   Features: {len(self.feature_names)}")

        return train_loader, val_loader, test_loader

    def _build_model(self) -> nn.Module:
        """Build enhanced model with calibration"""

        input_dim = len(self.feature_names)

        model = EnhancedPlayerPropsNetwork(
            input_dim=input_dim,
            hidden_dim=self.config.hidden_dim,
            num_layers=self.config.num_layers,
            dropout_rate=self.config.dropout_rate,
            use_calibration=self.config.use_calibration_layer
        ).to(self.device)

        logger.info(f"🏗️ Enhanced model built:")
        logger.info(f"   Input dim: {input_dim}")
        logger.info(f"   Hidden dim: {self.config.hidden_dim}")
        logger.info(f"   Layers: {self.config.num_layers}")
        logger.info(f"   Dropout: {self.config.dropout_rate}")
        logger.info(f"   Calibration: {self.config.use_calibration_layer}")
        logger.info(f"   Parameters: {sum(p.numel() for p in model.parameters()):,}")

        return model

    def _get_loss_function(self) -> nn.Module:
        """Get enhanced loss function"""

        if self.config.loss_function == "huber":
            return nn.HuberLoss(delta=self.config.huber_delta)
        elif self.config.loss_function == "quantile":
            return QuantileLoss(alpha=self.config.quantile_alpha)
        elif self.config.loss_function == "mse":
            return nn.MSELoss()
        else:
            logger.warning(f"⚠️ Unknown loss function {self.config.loss_function}, using MSE")
            return nn.MSELoss()

    def _apply_label_smoothing(self, targets: torch.Tensor) -> torch.Tensor:
        """Apply label smoothing to targets"""

        if self.config.label_smoothing > 0:
            noise = torch.randn_like(targets) * self.config.label_smoothing
            targets = targets + noise

        return targets

    async def train_ensemble(self) -> Dict[str, Any]:
        """Train ensemble of models with different seeds"""

        logger.info(f"🎲 Training ensemble of {self.config.ensemble_size} models")

        ensemble_results = []

        for i, seed in enumerate(self.config.ensemble_seeds):
            logger.info(f"🎯 Training ensemble model {i+1}/{self.config.ensemble_size} (seed={seed})")

            # Set seed for reproducibility
            torch.manual_seed(seed)
            np.random.seed(seed)

            # Train single model
            result = await self.train()

            # Store model
            model_path = f"{self.config.model_save_path}_ensemble_{i}.pt"
            torch.save({
                'model_state_dict': self.model.state_dict(),
                'config': self.config.__dict__,
                'feature_names': self.feature_names,
                'ensemble_index': i,
                'seed': seed,
                'result': result
            }, model_path)

            ensemble_results.append(result)
            logger.info(f"✅ Ensemble model {i+1} saved: {model_path}")

        # Calculate ensemble statistics
        ensemble_stats = {
            'ensemble_size': self.config.ensemble_size,
            'individual_results': ensemble_results,
            'mean_val_loss': np.mean([r['best_val_loss'] for r in ensemble_results]),
            'std_val_loss': np.std([r['best_val_loss'] for r in ensemble_results]),
            'mean_test_r2': np.mean([r.get('test_r2', 0) for r in ensemble_results]),
            'std_test_r2': np.std([r.get('test_r2', 0) for r in ensemble_results])
        }

        logger.info(f"🎉 Ensemble training complete!")
        logger.info(f"   Mean val loss: {ensemble_stats['mean_val_loss']:.4f} ± {ensemble_stats['std_val_loss']:.4f}")
        logger.info(f"   Mean test R²: {ensemble_stats['mean_test_r2']:.4f} ± {ensemble_stats['std_test_r2']:.4f}")

        return ensemble_stats

class QuantileLoss(nn.Module):
    """Quantile loss for robust regression"""

    def __init__(self, alpha: float = 0.5):
        super().__init__()
        self.alpha = alpha

    def forward(self, predictions: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        errors = targets - predictions
        loss = torch.max(self.alpha * errors, (self.alpha - 1) * errors)
        return loss.mean()

def create_enhanced_config(league: str = "WNBA", prop_type: str = "points", **kwargs) -> EnhancedPlayerPropsConfig:
    """Create enhanced configuration with optimized defaults"""

    # Enhanced defaults
    enhanced_params = {
        'league': league,
        'prop_type': prop_type,

        # Model architecture (larger for enhanced features)
        'hidden_dim': 256,
        'num_layers': 4,
        'dropout_rate': 0.4,
        'use_batch_norm': True,

        # Training parameters
        'batch_size': 64,
        'learning_rate': 0.0005,  # Slightly lower for stability
        'weight_decay': 0.01,
        'num_epochs': 150,  # More epochs for complex model
        'early_stopping_patience': 20,

        # Enhanced features
        'add_interaction_features': True,
        'feature_selection_threshold': 0.05,
        'max_features': 50,

        # Ensemble
        'ensemble_size': 5,

        # Loss and regularization
        'loss_function': "huber",
        'huber_delta': 1.0,
        'label_smoothing': 0.1,

        # Data augmentation
        'noise_augmentation': True,
        'noise_std': 0.05,
        'augmentation_ratio': 0.2,

        # Calibration
        'use_calibration_layer': True,

        # Player props specific
        'prediction_target': "regression",
        'output_activation': "linear",
        'include_player_features': True,
        'include_matchup_features': True,
        'include_situational_features': True,
        'include_recent_form': True
    }

    # Override with provided kwargs
    enhanced_params.update(kwargs)

    return EnhancedPlayerPropsConfig(**enhanced_params)

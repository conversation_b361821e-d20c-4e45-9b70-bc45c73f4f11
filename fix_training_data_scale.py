#!/usr/bin/env python3
"""
Fix Neural Model Training Data Scale Issue.
Convert cumulative season totals to per-game averages to fix the fundamental scale mismatch.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
from pathlib import Path
import shutil
from typing import Dict, List

# Configure logging
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_training_data_scale():
    """Convert season totals to per-game averages in training data"""
    print("🔧 FIXING TRAINING DATA SCALE ISSUE")
    print("=" * 60)
    
    # Define the stat columns that need to be converted from season totals to per-game
    per_game_stats = [
        'points', 'rebounds', 'assists', 'steals', 'blocks', 'threes',
        'points_per_minute', 'rebounds_per_minute', 'assists_per_minute', 
        'steals_per_minute', 'blocks_per_minute', 'threes_per_minute',
        'total_stats', 'defensive_stats', 'offensive_stats'
    ]
    
    # Find all training data files
    training_files = [
        'data/real_wnba_points_training_data.csv',
        'data/real_wnba_rebounds_training_data.csv', 
        'data/real_wnba_assists_training_data.csv',
        'data/real_wnba_steals_training_data.csv',
        'data/real_wnba_blocks_training_data.csv',
        'data/real_wnba_threes_training_data.csv'
    ]
    
    for file_path in training_files:
        if Path(file_path).exists():
            print(f"\n📄 Processing {file_path}")
            
            # Create backup
            backup_path = file_path.replace('.csv', '_backup_season_totals.csv')
            if not Path(backup_path).exists():
                shutil.copy2(file_path, backup_path)
                print(f"   💾 Created backup: {backup_path}")
            
            # Load data
            df = pd.read_csv(file_path)
            print(f"   📊 Loaded {len(df)} samples")
            
            # Check if games_played column exists
            if 'games_played' not in df.columns:
                print(f"   ❌ No games_played column found - skipping")
                continue
            
            # Show original scale
            target_col = 'target' if 'target' in df.columns else per_game_stats[0]
            if target_col in df.columns:
                original_mean = df[target_col].mean()
                original_std = df[target_col].std()
                print(f"   📈 Original {target_col}: mean={original_mean:.2f}, std={original_std:.2f}")
            
            # Convert season totals to per-game averages
            df_fixed = df.copy()

            # Handle division by zero
            games_played = df_fixed['games_played'].replace(0, 1)  # Avoid division by zero

            for stat in per_game_stats:
                if stat in df_fixed.columns:
                    # Convert to per-game by dividing by games_played
                    df_fixed[stat] = df_fixed[stat] / games_played
                    print(f"   ✅ Converted {stat} to per-game averages")

            # CRITICAL: Also fix the target column which contains the actual prediction target
            if 'target' in df_fixed.columns:
                df_fixed['target'] = df_fixed['target'] / games_played
                print(f"   ✅ Converted target column to per-game averages")
            
            # Show new scale
            if target_col in df_fixed.columns:
                new_mean = df_fixed[target_col].mean()
                new_std = df_fixed[target_col].std()
                print(f"   📈 Fixed {target_col}: mean={new_mean:.2f}, std={new_std:.2f}")
                
                # Validate the conversion makes sense
                if 'points' in target_col.lower():
                    if new_mean < 5 or new_mean > 25:
                        print(f"   ⚠️  WARNING: Points per game {new_mean:.2f} seems unrealistic")
                    else:
                        print(f"   ✅ Points per game {new_mean:.2f} looks realistic for WNBA")
                
                elif 'rebounds' in target_col.lower():
                    if new_mean < 2 or new_mean > 15:
                        print(f"   ⚠️  WARNING: Rebounds per game {new_mean:.2f} seems unrealistic")
                    else:
                        print(f"   ✅ Rebounds per game {new_mean:.2f} looks realistic for WNBA")
            
            # Save fixed data
            df_fixed.to_csv(file_path, index=False)
            print(f"   💾 Saved fixed data to {file_path}")
            
        else:
            print(f"   ❌ File not found: {file_path}")

def validate_fixed_data():
    """Validate that the fixed training data has realistic per-game scales"""
    print("\n🔍 VALIDATING FIXED TRAINING DATA")
    print("=" * 60)
    
    training_files = [
        ('data/real_wnba_points_training_data.csv', 'points', 8, 20),
        ('data/real_wnba_rebounds_training_data.csv', 'rebounds', 3, 12),
        ('data/real_wnba_assists_training_data.csv', 'assists', 1, 8),
        ('data/real_wnba_steals_training_data.csv', 'steals', 0.5, 3),
        ('data/real_wnba_blocks_training_data.csv', 'blocks', 0.1, 2),
        ('data/real_wnba_threes_training_data.csv', 'threes', 0, 4)
    ]
    
    for file_path, stat_name, min_expected, max_expected in training_files:
        if Path(file_path).exists():
            df = pd.read_csv(file_path)
            
            # Check target column
            target_col = 'target' if 'target' in df.columns else stat_name
            if target_col in df.columns:
                mean_val = df[target_col].mean()
                std_val = df[target_col].std()
                min_val = df[target_col].min()
                max_val = df[target_col].max()
                
                print(f"\n📊 {stat_name.upper()} validation:")
                print(f"   Mean: {mean_val:.2f} (expected: {min_expected}-{max_expected})")
                print(f"   Std: {std_val:.2f}")
                print(f"   Range: {min_val:.2f} - {max_val:.2f}")
                
                # Validate range
                if min_expected <= mean_val <= max_expected:
                    print(f"   ✅ Mean {mean_val:.2f} is within expected range")
                else:
                    print(f"   ⚠️  Mean {mean_val:.2f} is outside expected range {min_expected}-{max_expected}")
                
                # Check for outliers
                outliers = df[df[target_col] > max_expected * 2]
                if len(outliers) > 0:
                    print(f"   ⚠️  Found {len(outliers)} potential outliers (>{max_expected * 2:.1f})")
                else:
                    print(f"   ✅ No extreme outliers found")

def create_corrected_model_config():
    """Create a configuration note for retraining models with corrected data"""
    print("\n📝 CREATING MODEL RETRAINING GUIDANCE")
    print("=" * 60)
    
    config_note = """
# NEURAL MODEL RETRAINING REQUIRED

## Issue Identified
- All existing neural models were trained on cumulative season totals
- Models are being used for per-game predictions
- This causes systematic under-prediction (8.52 MAE for points)

## Training Data Fixed
- Converted season totals to per-game averages
- Points: ~158 season total to ~12-15 per game
- Rebounds: ~83 season total to ~6-8 per game
- Assists: ~48 season total to ~3-4 per game

## Next Steps Required
1. Retrain all neural models using fixed per-game training data
2. Update target scaler parameters to reflect per-game scale
3. Validate predictions against recent boxscore data
4. Expected improvements:
   - Points predictions: 8.52 MAE to ~2-3 MAE
   - Better star player prediction accuracy
   - Proper outlier game handling

## Model Training Command
```bash
python src/neural_cortex/player_props_neural_pipeline.py --prop_type points --league WNBA --retrain
```
"""
    
    with open('NEURAL_MODEL_RETRAINING_REQUIRED.md', 'w', encoding='utf-8') as f:
        f.write(config_note)
    
    print("   📄 Created NEURAL_MODEL_RETRAINING_REQUIRED.md")
    print("   🎯 Models must be retrained with corrected per-game data")

if __name__ == "__main__":
    print("🚀 FIXING NEURAL MODEL TRAINING DATA SCALE")
    print("=" * 80)
    
    # Step 1: Fix training data scale
    fix_training_data_scale()
    
    # Step 2: Validate fixed data
    validate_fixed_data()
    
    # Step 3: Create retraining guidance
    create_corrected_model_config()
    
    print("\n" + "=" * 80)
    print("🎯 TRAINING DATA SCALE FIX COMPLETE")
    print("⚠️  CRITICAL: Neural models must be retrained with corrected data!")

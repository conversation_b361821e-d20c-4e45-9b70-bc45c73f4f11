#!/usr/bin/env python3
"""
🎯 TEST ENHANCED POINTS MODEL VS ACTUAL WNBA BOXSCORES
====================================================

This script tests the enhanced points model (96.55% R²) against actual WNBA boxscore data
to validate the real-world performance improvement over the baseline 66.7% accuracy.

Tests against two recent WNBA games:
- Game 1022300200: Las Vegas Aces vs Chicago Sky
- Game 1022300150: New York Liberty vs Las Vegas Aces
"""

import sys
import pandas as pd
import numpy as np
import asyncio
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add project root to path
sys.path.append('.')

from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService

def load_wnba_boxscore_data():
    """Load actual WNBA boxscore data from recent games"""
    logger.info("📊 Loading WNBA boxscore data from recent games...")
    
    # Two recent WNBA games
    game_files = [
        "data/smart_10year_historical/boxscore_traditional_v2_wnba_game_id_1022300200.csv",  # LVA vs CHI
        "data/smart_10year_historical/boxscore_traditional_v2_wnba_game_id_1022300150.csv"   # NYL vs LAS
    ]
    
    actual_results = []
    
    for game_file in game_files:
        if Path(game_file).exists():
            logger.info(f"📄 Loading {game_file}")
            df = pd.read_csv(game_file)
            
            # Process each player
            for _, player in df.iterrows():
                try:
                    # Skip players who didn't play
                    if pd.isna(player['MIN']) or player['MIN'] == 0 or 'DNP' in str(player.get('COMMENT', '')):
                        continue
                    
                    # Extract minutes (convert from MM:SS format)
                    minutes_str = str(player['MIN'])
                    if ':' in minutes_str:
                        parts = minutes_str.split(':')
                        minutes = float(parts[0]) + float(parts[1]) / 60.0
                    else:
                        minutes = float(minutes_str)
                    
                    # Only include players with meaningful minutes
                    if minutes < 5.0:
                        continue
                    
                    actual_stats = {
                        'player_name': player['PLAYER_NAME'],
                        'team': player['TEAM_ABBREVIATION'],
                        'game_id': player['GAME_ID'],
                        'minutes': minutes,
                        'actual_points': float(player['PTS']) if pd.notna(player['PTS']) else 0.0,
                        'actual_rebounds': float(player['REB']) if pd.notna(player['REB']) else 0.0,
                        'actual_assists': float(player['AST']) if pd.notna(player['AST']) else 0.0,
                        'actual_steals': float(player['STL']) if pd.notna(player['STL']) else 0.0,
                        'actual_blocks': float(player['BLK']) if pd.notna(player['BLK']) else 0.0,
                        'actual_threes': float(player['FG3M']) if pd.notna(player['FG3M']) else 0.0,
                    }
                    
                    actual_results.append(actual_stats)
                    
                except Exception as e:
                    logger.warning(f"⚠️ Error processing {player.get('PLAYER_NAME', 'Unknown')}: {e}")
                    continue
    
    logger.info(f"✅ Loaded {len(actual_results)} player performances from {len(game_files)} games")
    return actual_results

async def test_enhanced_points_model(actual_results):
    """Test the enhanced points model against actual results"""
    logger.info("\n🧠 Testing Enhanced Points Model...")
    logger.info("=" * 60)
    
    try:
        # Initialize service
        service = UnifiedNeuralPredictionService(league='WNBA')
        
        predictions_vs_actual = []
        
        for player_data in actual_results:
            try:
                logger.info(f"🔮 Predicting for {player_data['player_name']} ({player_data['team']})...")
                
                # Generate prediction
                result = await service.predict_player_props(
                    player_name=player_data['player_name'],
                    team_name=player_data['team'],
                    opponent_name="Opponent",  # Generic opponent for testing
                    game_date="2024-09-15"
                )
                
                if result.success and player_data['player_name'] in result.player_props:
                    predictions = result.player_props[player_data['player_name']]
                    confidences = result.confidence_scores.get(player_data['player_name'], {})
                    
                    comparison = {
                        'player_name': player_data['player_name'],
                        'team': player_data['team'],
                        'minutes': player_data['minutes'],
                        
                        # Points (focus of this test)
                        'pred_points': predictions.get('points', 0.0),
                        'actual_points': player_data['actual_points'],
                        'conf_points': confidences.get('points', 0.0),
                        
                        # Other stats for comparison
                        'pred_rebounds': predictions.get('rebounds', 0.0),
                        'actual_rebounds': player_data['actual_rebounds'],
                        'pred_assists': predictions.get('assists', 0.0),
                        'actual_assists': player_data['actual_assists'],
                        'pred_steals': predictions.get('steals', 0.0),
                        'actual_steals': player_data['actual_steals'],
                        'pred_blocks': predictions.get('blocks', 0.0),
                        'actual_blocks': player_data['actual_blocks'],
                        'pred_threes': predictions.get('threes', 0.0),
                        'actual_threes': player_data['actual_threes'],
                    }
                    
                    predictions_vs_actual.append(comparison)
                    
                else:
                    logger.warning(f"  ❌ No predictions for {player_data['player_name']}")
                    
            except Exception as e:
                logger.error(f"  ❌ Prediction failed for {player_data['player_name']}: {e}")
                continue
        
        logger.info(f"✅ Generated predictions for {len(predictions_vs_actual)} players")
        return predictions_vs_actual
        
    except Exception as e:
        logger.error(f"❌ Error in enhanced points model testing: {e}")
        return []

def analyze_points_accuracy(predictions_vs_actual):
    """Analyze the accuracy of points predictions specifically"""
    logger.info("\n📈 ENHANCED POINTS MODEL ACCURACY ANALYSIS")
    logger.info("=" * 60)
    
    if not predictions_vs_actual:
        logger.error("❌ No predictions to analyze")
        return
    
    # Focus on points predictions
    points_errors = []
    points_within_1 = 0
    points_within_2 = 0
    points_within_3 = 0
    
    logger.info(f"{'Player':<20} {'Team':<4} {'Min':<4} {'Actual':<6} {'Pred':<6} {'Error':<6} {'Conf':<5}")
    logger.info("-" * 70)
    
    for player in predictions_vs_actual:
        actual = player['actual_points']
        predicted = player['pred_points']
        confidence = player['conf_points']
        
        error = abs(actual - predicted)
        points_errors.append(error)
        
        if error <= 1.0:
            points_within_1 += 1
        if error <= 2.0:
            points_within_2 += 1
        if error <= 3.0:
            points_within_3 += 1
        
        logger.info(f"{player['player_name']:<20} {player['team']:<4} {player['minutes']:<4.0f} "
                   f"{actual:<6.1f} {predicted:<6.1f} {error:<6.1f} {confidence:<5.2f}")
    
    # Calculate overall statistics
    total_players = len(predictions_vs_actual)
    avg_error = np.mean(points_errors)
    median_error = np.median(points_errors)
    
    accuracy_1pt = (points_within_1 / total_players) * 100
    accuracy_2pt = (points_within_2 / total_players) * 100
    accuracy_3pt = (points_within_3 / total_players) * 100
    
    logger.info("\n🎯 ENHANCED POINTS MODEL RESULTS:")
    logger.info(f"  📊 Total Players Tested: {total_players}")
    logger.info(f"  📊 Average Error: {avg_error:.2f} points")
    logger.info(f"  📊 Median Error: {median_error:.2f} points")
    logger.info(f"  ✅ Within 1 point: {points_within_1}/{total_players} ({accuracy_1pt:.1f}%)")
    logger.info(f"  ✅ Within 2 points: {points_within_2}/{total_players} ({accuracy_2pt:.1f}%)")
    logger.info(f"  ✅ Within 3 points: {points_within_3}/{total_players} ({accuracy_3pt:.1f}%)")
    
    # Compare to baseline
    logger.info(f"\n📈 IMPROVEMENT vs BASELINE (66.7%):")
    if accuracy_2pt > 66.7:
        improvement = accuracy_2pt - 66.7
        logger.info(f"  🚀 IMPROVED by {improvement:.1f} percentage points!")
        logger.info(f"  🎯 New accuracy: {accuracy_2pt:.1f}% (was 66.7%)")
    else:
        decline = 66.7 - accuracy_2pt
        logger.info(f"  ⚠️ Declined by {decline:.1f} percentage points")
    
    return {
        'total_players': total_players,
        'avg_error': avg_error,
        'median_error': median_error,
        'accuracy_1pt': accuracy_1pt,
        'accuracy_2pt': accuracy_2pt,
        'accuracy_3pt': accuracy_3pt,
        'improvement': accuracy_2pt - 66.7
    }

async def main():
    """Main test function"""
    logger.info("🏀 ENHANCED POINTS MODEL VALIDATION")
    logger.info("Testing against actual WNBA boxscore data")
    logger.info("=" * 60)
    
    # Load actual results
    actual_results = load_wnba_boxscore_data()
    
    if not actual_results:
        logger.error("❌ No actual game results found")
        return
    
    # Test enhanced points model
    predictions_vs_actual = await test_enhanced_points_model(actual_results)
    
    if not predictions_vs_actual:
        logger.error("❌ No predictions generated")
        return
    
    # Analyze points accuracy
    results = analyze_points_accuracy(predictions_vs_actual)
    
    logger.info("\n" + "=" * 60)
    logger.info("✅ Enhanced points model validation completed!")
    
    return results

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
Test Game Model Loading
Check what game models are available and test loading them
"""

import sys
import os
import logging
import torch
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add project root to path
sys.path.append('.')

from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService

def check_game_model_files():
    """Check what game model files exist"""
    logger.info("🔍 Checking for Game Model Files")
    logger.info("=" * 50)
    
    model_dirs = [
        "models/wnba_neural_models",
        "models/real_basketball_models", 
        "models/balanced_models",
        "models"
    ]
    
    for model_dir in model_dirs:
        path = Path(model_dir)
        if path.exists():
            logger.info(f"\n📁 {model_dir}:")
            for file in path.glob("*.pt"):
                logger.info(f"  • {file.name}")
                
                # Try to load and inspect
                try:
                    checkpoint = torch.load(file, map_location='cpu')
                    keys = list(checkpoint.keys())
                    logger.info(f"    Keys: {keys}")
                    
                    if 'config' in checkpoint:
                        config = checkpoint['config']
                        input_dim = config.get('input_dim', 'unknown')
                        logger.info(f"    Input dim: {input_dim}")
                        
                        # Check if this looks like a game model
                        if 'output_dim' in config:
                            output_dim = config.get('output_dim')
                            logger.info(f"    Output dim: {output_dim}")
                            if output_dim == 2:
                                logger.info(f"    🎯 POTENTIAL GAME MODEL (binary classification)")
                        
                except Exception as e:
                    logger.warning(f"    ⚠️ Failed to load: {e}")
        else:
            logger.info(f"📁 {model_dir}: Not found")

async def test_unified_service_game_loading():
    """Test the unified service game model loading"""
    logger.info("\n🏀 Testing Unified Service Game Model Loading")
    logger.info("=" * 60)

    try:
        # Initialize service
        service = UnifiedNeuralPredictionService(league='WNBA')

        # Initialize models
        await service.initialize()

        # Check if game model loaded
        if service.game_model is not None:
            logger.info("✅ Game model loaded successfully!")
            logger.info(f"📊 Game model type: {type(service.game_model)}")
            logger.info(f"📊 Game scaler: {service.game_scaler is not None}")
        else:
            logger.warning("⚠️ Game model not loaded")

            # Try manual loading
            logger.info("🔧 Attempting manual game model loading...")

            # Check different possible paths
            possible_paths = [
                "models/wnba_neural_models/best_model.pt",
                "models/real_basketball_models/best_points_model.pt",  # Might be misnamed
                "models/balanced_models/best_model.pt"
            ]

            for path in possible_paths:
                if Path(path).exists():
                    logger.info(f"🔍 Trying to load: {path}")
                    try:
                        success = await service._load_game_model(path)
                        if success:
                            logger.info(f"✅ Successfully loaded game model from: {path}")
                            break
                    except Exception as e:
                        logger.warning(f"❌ Failed to load {path}: {e}")

    except Exception as e:
        logger.error(f"❌ Failed to initialize service: {e}")

async def test_game_prediction():
    """Test making a game prediction"""
    logger.info("\n🎯 Testing Game Prediction")
    logger.info("=" * 40)

    try:
        service = UnifiedNeuralPredictionService(league='WNBA')
        await service.initialize()

        # Test game data
        game_data = {
            'home_team': 'Las Vegas Aces',
            'away_team': 'Minnesota Lynx',
            'league': 'WNBA'
        }

        # Try to predict
        prediction = await service._predict_game_outcome(game_data)

        logger.info("🎯 Game Prediction Result:")
        for key, value in prediction.items():
            logger.info(f"  {key}: {value}")

    except Exception as e:
        logger.error(f"❌ Game prediction failed: {e}")

async def main():
    """Main function"""
    logger.info("🏀 Game Model Loading Test")
    logger.info("=" * 50)

    # Step 1: Check what files exist
    check_game_model_files()

    # Step 2: Test unified service loading
    await test_unified_service_game_loading()

    # Step 3: Test prediction
    await test_game_prediction()

    logger.info("\n✅ Game model testing complete!")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())

#!/usr/bin/env python3
"""
🔧 FIX ENHANCED POINTS MODEL INFERENCE
=====================================

CRITICAL FIX: The enhanced points model was trained on SEASON TOTALS but inference
expects PER-GAME predictions. This causes systematic bias:

- Stars: Model predicts 8-10 points (season totals) vs 18+ expected per-game
- Bench: Model predicts 15-25 points (season totals) vs 0-5 expected per-game

Solution: Convert model predictions from season totals to per-game values.
"""

import torch
import numpy as np
import pandas as pd
from pathlib import Path
import logging
from sklearn.preprocessing import StandardScaler

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FixedEnhancedPointsPredictor:
    """Fixed enhanced points predictor that handles season total -> per-game conversion"""
    
    def __init__(self):
        self.model = None
        self.feature_scaler = None
        self.target_scaler = None
        self.feature_list = None
        self.config = None
        self.loaded = False
        
    def load_model(self):
        """Load the enhanced points model"""
        model_path = "models/enhanced_basketball_models/best_points_model.pt"
        
        if not Path(model_path).exists():
            raise FileNotFoundError(f"Enhanced model not found: {model_path}")
        
        try:
            checkpoint = torch.load(model_path, map_location='cpu')
            
            # Get feature list and config
            self.feature_list = checkpoint['feature_list']
            self.config = checkpoint['config']
            
            # Setup feature scaler
            feature_params = checkpoint['feature_scaler_params']
            self.feature_scaler = StandardScaler()
            for attr in ['mean_', 'scale_', 'var_', 'n_features_in_', 'n_samples_seen_']:
                if attr in feature_params:
                    setattr(self.feature_scaler, attr, feature_params[attr])
            
            # Setup target scaler
            target_params = checkpoint['target_scaler_params']
            self.target_scaler = StandardScaler()
            for attr in ['mean_', 'scale_', 'var_', 'n_features_in_', 'n_samples_seen_']:
                if attr in target_params:
                    setattr(self.target_scaler, attr, target_params[attr])
            
            # Load model architecture
            from torch import nn
            
            class EnhancedPointsModel(nn.Module):
                def __init__(self, input_dim, hidden_dim, num_layers, dropout_rate):
                    super().__init__()
                    layers = []
                    
                    # Input layer
                    layers.append(nn.Linear(input_dim, hidden_dim))
                    layers.append(nn.BatchNorm1d(hidden_dim))
                    layers.append(nn.ReLU())
                    layers.append(nn.Dropout(dropout_rate))
                    
                    # Hidden layers
                    for _ in range(num_layers - 1):
                        layers.append(nn.Linear(hidden_dim, hidden_dim))
                        layers.append(nn.BatchNorm1d(hidden_dim))
                        layers.append(nn.ReLU())
                        layers.append(nn.Dropout(dropout_rate))
                    
                    # Output layer
                    layers.append(nn.Linear(hidden_dim, 1))
                    
                    self.network = nn.Sequential(*layers)
                
                def forward(self, x):
                    return self.network(x)
            
            # Create model
            self.model = EnhancedPointsModel(
                input_dim=self.config['input_dim'],
                hidden_dim=self.config['hidden_dim'],
                num_layers=self.config['num_layers'],
                dropout_rate=self.config['dropout_rate']
            )
            
            # Load weights
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model.eval()
            
            self.loaded = True
            logger.info("✅ Enhanced points model loaded successfully")
            
        except Exception as e:
            logger.error(f"❌ Error loading model: {e}")
            raise
    
    def create_feature_vector(self, player_data):
        """Create feature vector matching training format"""
        
        # Estimate season totals from per-game data
        games_played = player_data.get('games_played', 30)  # Assume 30 games for WNBA
        minutes_per_game = player_data.get('minutes_per_game', 20)
        
        # Convert per-game stats to season totals (matching training data)
        season_points = player_data.get('points_per_game', 10) * games_played
        season_rebounds = player_data.get('rebounds_per_game', 5) * games_played
        season_assists = player_data.get('assists_per_game', 3) * games_played
        season_steals = player_data.get('steals_per_game', 1) * games_played
        season_blocks = player_data.get('blocks_per_game', 0.5) * games_played
        season_threes = player_data.get('threes_per_game', 1) * games_played
        
        # Total season minutes
        total_minutes = minutes_per_game * games_played
        
        # Create feature vector in exact order from training
        features = []
        
        for feature_name in self.feature_list:
            if feature_name == 'points':
                features.append(season_points)
            elif feature_name == 'rebounds':
                features.append(season_rebounds)
            elif feature_name == 'assists':
                features.append(season_assists)
            elif feature_name == 'steals':
                features.append(season_steals)
            elif feature_name == 'blocks':
                features.append(season_blocks)
            elif feature_name == 'threes':
                features.append(season_threes)
            elif feature_name == 'games_played':
                features.append(games_played)
            elif feature_name == 'minutes_per_game':
                features.append(total_minutes)  # CRITICAL: Use total minutes, not per-game
            elif feature_name == 'field_goal_percentage':
                features.append(player_data.get('fg_pct', 0.45))
            elif feature_name == 'free_throw_percentage':
                features.append(player_data.get('ft_pct', 0.75))
            elif feature_name == 'age':
                features.append(player_data.get('age', 25))
            elif feature_name == 'usage_rate':
                features.append(player_data.get('usage_rate', 20))
            elif feature_name == 'field_goal_attempts':
                features.append(player_data.get('fga_per_game', 8) * games_played)
            elif feature_name == 'three_point_attempts':
                features.append(player_data.get('fg3a_per_game', 3) * games_played)
            elif feature_name == 'free_throw_attempts':
                features.append(player_data.get('fta_per_game', 2) * games_played)
            elif feature_name == 'recent_points_avg_5':
                features.append(player_data.get('recent_points_5', season_points / games_played) * games_played)
            elif feature_name == 'recent_points_avg_10':
                features.append(player_data.get('recent_points_10', season_points / games_played) * games_played)
            elif feature_name == 'team_pace':
                features.append(player_data.get('team_pace', 85))
            elif feature_name == 'opponent_def_rating':
                features.append(player_data.get('opp_def_rating', 105))
            elif feature_name == 'home_game':
                features.append(player_data.get('home_game', 0.5))
            elif feature_name == 'starter_status':
                features.append(1.0 if minutes_per_game > 20 else 0.0)
            else:
                # Default values for other features
                if 'tier' in feature_name:
                    features.append(2.0)  # Medium tier
                elif 'high_' in feature_name:
                    features.append(0.0)  # Not high performer
                elif 'per_minute' in feature_name:
                    features.append(0.3)  # Average per-minute rate
                elif 'stats' in feature_name:
                    features.append(10.0)  # Average total stats
                else:
                    features.append(0.0)  # Default
        
        return np.array(features)
    
    def predict_points_per_game(self, player_data):
        """Predict points per game (corrected from season totals)"""
        
        if not self.loaded:
            self.load_model()
        
        try:
            # Create feature vector
            features = self.create_feature_vector(player_data)
            
            # Scale features
            features_scaled = self.feature_scaler.transform(features.reshape(1, -1))
            features_tensor = torch.tensor(features_scaled, dtype=torch.float32)
            
            # Predict (season total)
            with torch.no_grad():
                raw_prediction = self.model(features_tensor).item()
            
            # Unscale to get season total points
            season_total_prediction = self.target_scaler.inverse_transform([[raw_prediction]])[0][0]
            
            # Convert to per-game prediction
            games_played = player_data.get('games_played', 30)
            per_game_prediction = season_total_prediction / games_played
            
            # Apply realistic bounds for WNBA per-game points
            per_game_prediction = max(0, min(per_game_prediction, 35))
            
            return per_game_prediction
            
        except Exception as e:
            logger.error(f"❌ Prediction error: {e}")
            return 10.0  # Default fallback

def test_fixed_predictions():
    """Test the fixed predictions against known players"""
    
    predictor = FixedEnhancedPointsPredictor()
    
    # Test cases based on actual WNBA players
    test_players = [
        {
            'name': 'A\'ja Wilson (Star)',
            'minutes_per_game': 34,
            'points_per_game': 18,  # Her actual average
            'rebounds_per_game': 10,
            'assists_per_game': 2,
            'steals_per_game': 1.5,
            'blocks_per_game': 2,
            'threes_per_game': 0.5,
            'usage_rate': 28,
            'fga_per_game': 16,
            'games_played': 30
        },
        {
            'name': 'Jackie Young (Star)',
            'minutes_per_game': 37,
            'points_per_game': 24,  # Her actual average
            'rebounds_per_game': 5,
            'assists_per_game': 5,
            'steals_per_game': 1,
            'blocks_per_game': 0.5,
            'threes_per_game': 2,
            'usage_rate': 25,
            'fga_per_game': 14,
            'games_played': 30
        },
        {
            'name': 'Kierstan Bell (Bench)',
            'minutes_per_game': 8,
            'points_per_game': 2,  # Bench player
            'rebounds_per_game': 1,
            'assists_per_game': 0.5,
            'steals_per_game': 0.2,
            'blocks_per_game': 0.1,
            'threes_per_game': 0.3,
            'usage_rate': 15,
            'fga_per_game': 3,
            'games_played': 25
        }
    ]
    
    logger.info("\n🧪 TESTING FIXED ENHANCED POINTS PREDICTIONS")
    logger.info("=" * 60)
    
    for player in test_players:
        prediction = predictor.predict_points_per_game(player)
        actual = player['points_per_game']
        error = abs(prediction - actual)
        
        logger.info(f"🏀 {player['name']}")
        logger.info(f"   Minutes: {player['minutes_per_game']:.1f}")
        logger.info(f"   Actual: {actual:.1f} points")
        logger.info(f"   Predicted: {prediction:.1f} points")
        logger.info(f"   Error: {error:.1f} points")
        logger.info(f"   Status: {'✅ GOOD' if error < 3 else '⚠️ HIGH ERROR'}")
        logger.info("")

def main():
    """Main function"""
    logger.info("🔧 FIXING ENHANCED POINTS MODEL INFERENCE")
    logger.info("=" * 60)
    
    # Test fixed predictions
    test_fixed_predictions()
    
    logger.info("✅ Fixed inference pipeline tested!")
    logger.info("📋 Next: Integrate into validation script")

if __name__ == "__main__":
    main()

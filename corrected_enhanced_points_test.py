#!/usr/bin/env python3
"""
🎯 CORRECTED ENHANCED POINTS MODEL TEST
======================================

CRITICAL DISCOVERY: Training data has MIXED SCALES:
- Points: Per-game values (mean ~6 ppg)  
- Minutes: Total season values (mean ~400 total)

This explains the systematic bias. The model expects:
- Input: Mixed scale features (per-game points, total minutes)
- Output: Per-game points prediction

Fixed inference pipeline to match training data format exactly.
"""

import torch
import numpy as np
import pandas as pd
from pathlib import Path
import logging
from sklearn.preprocessing import StandardScaler

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CorrectedEnhancedPointsPredictor:
    """Corrected predictor matching exact training data format"""
    
    def __init__(self):
        self.model = None
        self.feature_scaler = None
        self.target_scaler = None
        self.feature_list = None
        self.loaded = False
        
    def load_model(self):
        """Load the enhanced points model"""
        model_path = "models/enhanced_basketball_models/best_points_model.pt"
        
        if not Path(model_path).exists():
            raise FileNotFoundError(f"Enhanced model not found: {model_path}")
        
        try:
            checkpoint = torch.load(model_path, map_location='cpu')
            
            # Get feature list
            self.feature_list = checkpoint['feature_list']
            config = checkpoint['config']
            
            # Setup scalers
            feature_params = checkpoint['feature_scaler_params']
            self.feature_scaler = StandardScaler()
            for attr in ['mean_', 'scale_', 'var_', 'n_features_in_', 'n_samples_seen_']:
                if attr in feature_params:
                    setattr(self.feature_scaler, attr, feature_params[attr])
            
            target_params = checkpoint['target_scaler_params']
            self.target_scaler = StandardScaler()
            for attr in ['mean_', 'scale_', 'var_', 'n_features_in_', 'n_samples_seen_']:
                if attr in target_params:
                    setattr(self.target_scaler, attr, target_params[attr])
            
            # Load model
            from torch import nn
            
            class EnhancedPointsModel(nn.Module):
                def __init__(self, input_dim, hidden_dim, num_layers, dropout_rate):
                    super().__init__()
                    layers = []
                    
                    layers.append(nn.Linear(input_dim, hidden_dim))
                    layers.append(nn.BatchNorm1d(hidden_dim))
                    layers.append(nn.ReLU())
                    layers.append(nn.Dropout(dropout_rate))
                    
                    for _ in range(num_layers - 1):
                        layers.append(nn.Linear(hidden_dim, hidden_dim))
                        layers.append(nn.BatchNorm1d(hidden_dim))
                        layers.append(nn.ReLU())
                        layers.append(nn.Dropout(dropout_rate))
                    
                    layers.append(nn.Linear(hidden_dim, 1))
                    self.network = nn.Sequential(*layers)
                
                def forward(self, x):
                    return self.network(x)
            
            self.model = EnhancedPointsModel(
                input_dim=config['input_dim'],
                hidden_dim=config['hidden_dim'],
                num_layers=config['num_layers'],
                dropout_rate=config['dropout_rate']
            )
            
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model.eval()
            
            self.loaded = True
            logger.info("✅ Enhanced points model loaded successfully")
            
        except Exception as e:
            logger.error(f"❌ Error loading model: {e}")
            raise
    
    def create_feature_vector_corrected(self, player_data):
        """Create feature vector matching EXACT training format"""
        
        # Extract player stats (per-game format like training)
        points_pg = player_data.get('points_per_game', 10)
        rebounds_pg = player_data.get('rebounds_per_game', 5)
        assists_pg = player_data.get('assists_per_game', 3)
        steals_pg = player_data.get('steals_per_game', 1)
        blocks_pg = player_data.get('blocks_per_game', 0.5)
        threes_pg = player_data.get('threes_per_game', 1)
        
        # Games and minutes (total format like training)
        games_played = player_data.get('games_played', 30)
        minutes_per_game = player_data.get('minutes_per_game', 20)
        total_minutes = minutes_per_game * games_played  # CRITICAL: Total minutes like training
        
        # Create feature vector in exact training order
        features = []
        
        for feature_name in self.feature_list:
            if feature_name == 'points':
                features.append(points_pg)  # Per-game like training
            elif feature_name == 'rebounds':
                features.append(rebounds_pg)
            elif feature_name == 'assists':
                features.append(assists_pg)
            elif feature_name == 'steals':
                features.append(steals_pg)
            elif feature_name == 'blocks':
                features.append(blocks_pg)
            elif feature_name == 'threes':
                features.append(threes_pg)
            elif feature_name == 'games_played':
                features.append(games_played)
            elif feature_name == 'minutes_per_game':
                features.append(total_minutes)  # Total minutes like training
            elif feature_name == 'field_goal_percentage':
                features.append(player_data.get('fg_pct', 0.45))
            elif feature_name == 'free_throw_percentage':
                features.append(player_data.get('ft_pct', 0.75))
            elif feature_name == 'age':
                features.append(player_data.get('age', 25))
            elif feature_name == 'usage_rate':
                features.append(player_data.get('usage_rate', 20))
            elif feature_name == 'field_goal_attempts':
                features.append(player_data.get('fga_per_game', 8))  # Per-game
            elif feature_name == 'three_point_attempts':
                features.append(player_data.get('fg3a_per_game', 3))  # Per-game
            elif feature_name == 'free_throw_attempts':
                features.append(player_data.get('fta_per_game', 2))  # Per-game
            elif feature_name == 'recent_points_avg_5':
                features.append(player_data.get('recent_points_5', points_pg))
            elif feature_name == 'recent_points_avg_10':
                features.append(player_data.get('recent_points_10', points_pg))
            elif feature_name == 'team_pace':
                features.append(player_data.get('team_pace', 85))
            elif feature_name == 'opponent_def_rating':
                features.append(player_data.get('opp_def_rating', 105))
            elif feature_name == 'home_game':
                features.append(player_data.get('home_game', 0.5))
            elif feature_name == 'starter_status':
                features.append(1.0 if minutes_per_game > 20 else 0.0)
            else:
                # Default values for computed features
                if 'tier' in feature_name:
                    features.append(2.0)
                elif 'high_' in feature_name:
                    features.append(1.0 if points_pg > 15 else 0.0)
                elif 'per_minute' in feature_name:
                    if total_minutes > 0:
                        if 'points' in feature_name:
                            features.append(points_pg / minutes_per_game)
                        elif 'rebounds' in feature_name:
                            features.append(rebounds_pg / minutes_per_game)
                        elif 'assists' in feature_name:
                            features.append(assists_pg / minutes_per_game)
                        elif 'steals' in feature_name:
                            features.append(steals_pg / minutes_per_game)
                        elif 'blocks' in feature_name:
                            features.append(blocks_pg / minutes_per_game)
                        elif 'threes' in feature_name:
                            features.append(threes_pg / minutes_per_game)
                        else:
                            features.append(0.3)
                    else:
                        features.append(0.0)
                elif 'total_stats' in feature_name:
                    features.append(points_pg + rebounds_pg + assists_pg)
                elif 'defensive_stats' in feature_name:
                    features.append(steals_pg + blocks_pg)
                elif 'offensive_stats' in feature_name:
                    features.append(points_pg + assists_pg + threes_pg)
                else:
                    features.append(0.0)
        
        return np.array(features)
    
    def predict_points_per_game(self, player_data):
        """Predict points per game with corrected scaling"""
        
        if not self.loaded:
            self.load_model()
        
        try:
            # Create feature vector matching training format
            features = self.create_feature_vector_corrected(player_data)
            
            # Scale features
            features_scaled = self.feature_scaler.transform(features.reshape(1, -1))
            features_tensor = torch.tensor(features_scaled, dtype=torch.float32)
            
            # Predict
            with torch.no_grad():
                raw_prediction = self.model(features_tensor).item()
            
            # Unscale (model outputs per-game points directly)
            per_game_prediction = self.target_scaler.inverse_transform([[raw_prediction]])[0][0]
            
            # Apply realistic bounds
            per_game_prediction = max(0, min(per_game_prediction, 35))
            
            return per_game_prediction
            
        except Exception as e:
            logger.error(f"❌ Prediction error: {e}")
            return 10.0

def test_corrected_predictions():
    """Test corrected predictions"""
    
    predictor = CorrectedEnhancedPointsPredictor()
    
    # Test with actual WNBA boxscore data
    test_players = [
        {
            'name': 'A\'ja Wilson',
            'minutes_per_game': 34,
            'points_per_game': 18,  # Actual from boxscore
            'rebounds_per_game': 10,
            'assists_per_game': 0,
            'steals_per_game': 1,
            'blocks_per_game': 2,
            'threes_per_game': 0,
            'usage_rate': 28,
            'fga_per_game': 16,
            'fg_pct': 0.563,
            'games_played': 30
        },
        {
            'name': 'Jackie Young',
            'minutes_per_game': 37,
            'points_per_game': 24,  # Actual from boxscore
            'rebounds_per_game': 10,
            'assists_per_game': 5,
            'steals_per_game': 1,
            'blocks_per_game': 0,
            'threes_per_game': 1,
            'usage_rate': 25,
            'fga_per_game': 14,
            'fg_pct': 0.571,
            'games_played': 30
        },
        {
            'name': 'Kierstan Bell',
            'minutes_per_game': 8,
            'points_per_game': 0,  # Actual from boxscore
            'rebounds_per_game': 0,
            'assists_per_game': 0,
            'steals_per_game': 0,
            'blocks_per_game': 0,
            'threes_per_game': 0,
            'usage_rate': 15,
            'fga_per_game': 3,
            'fg_pct': 0.0,
            'games_played': 25
        }
    ]
    
    logger.info("\n🎯 TESTING CORRECTED ENHANCED POINTS PREDICTIONS")
    logger.info("=" * 60)
    
    total_error = 0
    count = 0
    
    for player in test_players:
        prediction = predictor.predict_points_per_game(player)
        actual = player['points_per_game']
        error = abs(prediction - actual)
        total_error += error
        count += 1
        
        logger.info(f"🏀 {player['name']}")
        logger.info(f"   Minutes: {player['minutes_per_game']:.1f}")
        logger.info(f"   Actual: {actual:.1f} points")
        logger.info(f"   Predicted: {prediction:.1f} points")
        logger.info(f"   Error: {error:.1f} points")
        logger.info(f"   Status: {'✅ GOOD' if error < 5 else '⚠️ HIGH ERROR'}")
        logger.info("")
    
    avg_error = total_error / count
    logger.info(f"📊 AVERAGE ERROR: {avg_error:.1f} points")
    
    if avg_error < 5:
        logger.info("✅ CORRECTED MODEL PERFORMANCE: GOOD")
    else:
        logger.info("⚠️ CORRECTED MODEL PERFORMANCE: NEEDS MORE WORK")

def main():
    """Main function"""
    logger.info("🎯 CORRECTED ENHANCED POINTS MODEL TEST")
    logger.info("=" * 60)
    
    test_corrected_predictions()
    
    logger.info("✅ Corrected inference pipeline tested!")

if __name__ == "__main__":
    main()

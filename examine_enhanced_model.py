#!/usr/bin/env python3
"""
🔍 EXAMINE ENHANCED POINTS MODEL
===============================

Direct examination of the enhanced points model checkpoint to understand
the feature alignment and scaling issues causing systematic bias.
"""

import torch
import numpy as np
import pandas as pd
from pathlib import Path

def examine_enhanced_model():
    """Examine the enhanced points model checkpoint"""
    
    model_path = "models/enhanced_basketball_models/best_points_model.pt"
    
    if not Path(model_path).exists():
        print(f"❌ Model not found: {model_path}")
        return None
    
    try:
        checkpoint = torch.load(model_path, map_location='cpu')
        print(f"✅ Loaded enhanced points model")
        print(f"🔑 Keys: {list(checkpoint.keys())}")
        
        # Feature list
        feature_list = checkpoint.get('feature_list', [])
        print(f"\n📝 FEATURE LIST ({len(feature_list)} features):")
        for i, feature in enumerate(feature_list):
            print(f"  {i+1:2d}. {feature}")
        
        # Feature scaler
        feature_scaler = checkpoint.get('feature_scaler_params', {})
        if feature_scaler:
            print(f"\n🔧 FEATURE SCALER:")
            print(f"  Keys: {list(feature_scaler.keys())}")
            
            if 'mean_' in feature_scaler and 'scale_' in feature_scaler:
                mean = np.array(feature_scaler['mean_'])
                scale = np.array(feature_scaler['scale_'])
                
                print(f"  📊 Features: {len(mean)}")
                print(f"  📊 Mean range: [{mean.min():.3f}, {mean.max():.3f}]")
                print(f"  📊 Scale range: [{scale.min():.3f}, {scale.max():.3f}]")
                
                # Key features
                key_features = ['minutes_per_game', 'usage_rate', 'field_goal_attempts', 'recent_points_avg_5']
                print(f"\n  🎯 KEY FEATURES:")
                for feature_name in key_features:
                    if feature_name in feature_list:
                        idx = feature_list.index(feature_name)
                        print(f"    {feature_name}: mean={mean[idx]:.3f}, scale={scale[idx]:.3f}")
        
        # Target scaler
        target_scaler = checkpoint.get('target_scaler_params', {})
        if target_scaler:
            print(f"\n🎯 TARGET SCALER:")
            print(f"  Keys: {list(target_scaler.keys())}")
            
            if 'mean_' in target_scaler and 'scale_' in target_scaler:
                target_mean = target_scaler['mean_']
                target_scale = target_scaler['scale_']
                
                # Handle array format
                if isinstance(target_mean, (list, np.ndarray)):
                    target_mean = target_mean[0]
                if isinstance(target_scale, (list, np.ndarray)):
                    target_scale = target_scale[0]
                
                print(f"  📊 Mean: {target_mean:.3f} points")
                print(f"  📊 Scale: {target_scale:.3f} points")
                
                # Test scaling
                print(f"\n  🧪 SCALING TEST:")
                for raw in [-2, -1, 0, 1, 2]:
                    unscaled = (raw * target_scale) + target_mean
                    print(f"    {raw:4.1f} -> {unscaled:6.1f} points")
        
        # Config
        config = checkpoint.get('config', {})
        if config:
            print(f"\n⚙️ CONFIG:")
            for key, value in config.items():
                print(f"  {key}: {value}")
        
        return checkpoint
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def check_training_data():
    """Check the training data used"""
    
    data_path = "data/real_wnba_points_training_data.csv"
    
    if not Path(data_path).exists():
        print(f"❌ Training data not found: {data_path}")
        return None
    
    try:
        df = pd.read_csv(data_path)
        print(f"\n📊 TRAINING DATA: {df.shape}")
        
        # Points statistics
        if 'points' in df.columns:
            points = df['points']
            print(f"  Points: mean={points.mean():.2f}, std={points.std():.2f}")
            print(f"  Points: min={points.min():.1f}, max={points.max():.1f}")
            
            # Check distribution
            print(f"  Points >20: {len(points[points > 20])} ({len(points[points > 20])/len(points)*100:.1f}%)")
            print(f"  Points <5: {len(points[points < 5])} ({len(points[points < 5])/len(points)*100:.1f}%)")
        
        # Minutes statistics
        if 'minutes_per_game' in df.columns:
            minutes = df['minutes_per_game']
            print(f"  Minutes: mean={minutes.mean():.2f}, std={minutes.std():.2f}")
            print(f"  Minutes: min={minutes.min():.1f}, max={minutes.max():.1f}")
        
        return df
        
    except Exception as e:
        print(f"❌ Error loading training data: {e}")
        return None

def main():
    """Main function"""
    print("🔍 ENHANCED POINTS MODEL EXAMINATION")
    print("=" * 50)
    
    # Examine model
    checkpoint = examine_enhanced_model()
    
    # Check training data
    training_data = check_training_data()
    
    if checkpoint and training_data is not None:
        print(f"\n✅ Analysis complete!")
        print(f"📋 Issues identified:")
        print(f"  - Need to check feature alignment in inference")
        print(f"  - Need to verify scaling is applied correctly")
        print(f"  - Need to add minutes-based filtering")

if __name__ == "__main__":
    main()

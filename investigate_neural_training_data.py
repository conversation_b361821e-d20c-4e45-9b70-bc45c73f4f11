#!/usr/bin/env python3
"""
Investigate Neural Model Training Data and Architecture for Points Predictions.
This script analyzes training data distribution, model architecture, and implements points-specific adjustments.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
from pathlib import Path
from typing import Dict, List, Any, Tuple
import pickle
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler

# Configure logging
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def analyze_training_data_distribution():
    """Analyze the distribution of training data for points predictions"""
    print("🔍 ANALYZING TRAINING DATA DISTRIBUTION")
    print("=" * 60)
    
    # Check for training data files
    training_data_files = [
        "data/real_wnba_points_training_data.csv",
        "data/wnba_training_data.csv",
        "data/player_props_training_data.csv"
    ]
    
    for file_path in training_data_files:
        if Path(file_path).exists():
            print(f"\n📄 Analyzing {file_path}")
            df = pd.read_csv(file_path)
            
            # Look for points column
            points_cols = [col for col in df.columns if 'points' in col.lower() or 'pts' in col.lower()]
            print(f"   Points columns found: {points_cols}")
            
            if points_cols:
                points_col = points_cols[0]
                points_data = df[points_col].dropna()
                
                print(f"   📊 Points distribution analysis:")
                print(f"      Total samples: {len(points_data)}")
                print(f"      Mean: {points_data.mean():.2f}")
                print(f"      Median: {points_data.median():.2f}")
                print(f"      Std: {points_data.std():.2f}")
                print(f"      Min: {points_data.min():.2f}")
                print(f"      Max: {points_data.max():.2f}")
                print(f"      25th percentile: {points_data.quantile(0.25):.2f}")
                print(f"      75th percentile: {points_data.quantile(0.75):.2f}")
                print(f"      95th percentile: {points_data.quantile(0.95):.2f}")
                
                # Check for star player representation
                high_scorers = points_data[points_data > 20]
                print(f"      High scoring games (>20 pts): {len(high_scorers)} ({len(high_scorers)/len(points_data)*100:.1f}%)")
                
                elite_scorers = points_data[points_data > 25]
                print(f"      Elite scoring games (>25 pts): {len(elite_scorers)} ({len(elite_scorers)/len(points_data)*100:.1f}%)")
                
                # Check for player identification
                if 'player_name' in df.columns or 'PLAYER_NAME' in df.columns:
                    player_col = 'player_name' if 'player_name' in df.columns else 'PLAYER_NAME'
                    star_players = ['A\'ja Wilson', 'Breanna Stewart', 'Sabrina Ionescu', 'Kelsey Plum', 'Jackie Young']
                    
                    for star in star_players:
                        star_data = df[df[player_col].str.contains(star, na=False)][points_col].dropna()
                        if len(star_data) > 0:
                            print(f"      {star}: {len(star_data)} games, avg {star_data.mean():.1f} pts, max {star_data.max():.1f}")
                
                return points_data
        else:
            print(f"   ❌ File not found: {file_path}")
    
    return None

def analyze_model_checkpoints():
    """Analyze model checkpoints to understand architecture and scaling"""
    print("\n🔍 ANALYZING MODEL CHECKPOINTS")
    print("=" * 60)
    
    # Check different model paths
    model_paths = [
        "models/real_basketball_models/best_points_model.pt",
        "models/player_props/wnba_points/best_points_model.pt",
        "models/player_props/nba_points/best_points_model.pt",
        "models/balanced_models/wnba_points/best_points_model.pt",
        "models/wnba_neural_models/best_model.pt"
    ]
    
    for model_path in model_paths:
        if Path(model_path).exists():
            print(f"\n📄 Analyzing {model_path}")
            
            try:
                checkpoint = torch.load(model_path, map_location='cpu')
                
                print(f"   📋 Checkpoint keys: {list(checkpoint.keys())}")
                
                # Analyze config
                config = checkpoint.get('config', {})
                print(f"   ⚙️  Model config:")
                for key, value in config.items():
                    print(f"      {key}: {value}")
                
                # Analyze target scaler
                target_scaler_params = checkpoint.get('target_scaler_params', {})
                if target_scaler_params:
                    print(f"   🎯 Target scaler parameters:")
                    mean = target_scaler_params.get('mean_', [None])[0] if isinstance(target_scaler_params.get('mean_'), (list, np.ndarray)) else target_scaler_params.get('mean_')
                    scale = target_scaler_params.get('scale_', [None])[0] if isinstance(target_scaler_params.get('scale_'), (list, np.ndarray)) else target_scaler_params.get('scale_')
                    
                    print(f"      Mean: {mean}")
                    print(f"      Scale: {scale}")
                    
                    # Calculate original data statistics
                    if mean is not None and scale is not None:
                        print(f"      Original data mean: {mean:.2f} points")
                        print(f"      Original data std: {scale:.2f} points")
                        
                        # Check if this is realistic for WNBA
                        if mean < 8 or mean > 18:
                            print(f"      ⚠️  WARNING: Mean {mean:.2f} seems unrealistic for WNBA points")
                        if scale < 3 or scale > 12:
                            print(f"      ⚠️  WARNING: Std {scale:.2f} seems unrealistic for WNBA points")
                
                # Analyze feature scaler
                feature_scaler_params = checkpoint.get('feature_scaler_params', {})
                if feature_scaler_params:
                    n_features = feature_scaler_params.get('n_features_in_', 'Unknown')
                    print(f"   🔧 Feature scaler: {n_features} features")
                
                # Analyze training metrics
                if 'train_loss' in checkpoint:
                    print(f"   📈 Training loss: {checkpoint['train_loss']:.4f}")
                if 'val_loss' in checkpoint:
                    print(f"   📈 Validation loss: {checkpoint['val_loss']:.4f}")
                
                # Check feature list
                feature_list = checkpoint.get('feature_list', [])
                if feature_list:
                    print(f"   📝 Feature list ({len(feature_list)} features):")
                    for i, feature in enumerate(feature_list[:10]):  # Show first 10
                        print(f"      {i+1}. {feature}")
                    if len(feature_list) > 10:
                        print(f"      ... and {len(feature_list) - 10} more")
                
                return checkpoint
                
            except Exception as e:
                print(f"   ❌ Error loading checkpoint: {e}")
        else:
            print(f"   ❌ Model not found: {model_path}")
    
    return None

def compare_model_architectures():
    """Compare architectures between points and other stat models"""
    print("\n🔍 COMPARING MODEL ARCHITECTURES")
    print("=" * 60)
    
    prop_types = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']

    for prop_type in prop_types:
        # Check real_basketball_models first (most recent)
        model_path = f"models/real_basketball_models/best_{prop_type}_model.pt"
        if not Path(model_path).exists():
            model_path = f"models/player_props/wnba_{prop_type}/best_{prop_type}_model.pt"
        
        if Path(model_path).exists():
            try:
                checkpoint = torch.load(model_path, map_location='cpu')
                config = checkpoint.get('config', {})
                target_params = checkpoint.get('target_scaler_params', {})
                
                mean = target_params.get('mean_', [None])[0] if isinstance(target_params.get('mean_'), (list, np.ndarray)) else target_params.get('mean_')
                scale = target_params.get('scale_', [None])[0] if isinstance(target_params.get('scale_'), (list, np.ndarray)) else target_params.get('scale_')
                
                print(f"   📊 {prop_type.upper()}:")
                print(f"      Hidden dim: {config.get('hidden_dim', 'Unknown')}")
                print(f"      Layers: {config.get('num_layers', 'Unknown')}")
                print(f"      Dropout: {config.get('dropout_rate', 'Unknown')}")
                print(f"      Target mean: {mean:.2f}" if mean else "      Target mean: Unknown")
                print(f"      Target scale: {scale:.2f}" if scale else "      Target scale: Unknown")
                
                # Check training performance
                train_loss = checkpoint.get('train_loss', None)
                val_loss = checkpoint.get('val_loss', None)
                if train_loss and val_loss:
                    print(f"      Train loss: {train_loss:.4f}, Val loss: {val_loss:.4f}")
                
            except Exception as e:
                print(f"   ❌ {prop_type}: Error - {e}")
        else:
            print(f"   ❌ {prop_type}: Model not found")

def test_prediction_scaling():
    """Test if prediction scaling is working correctly"""
    print("\n🔍 TESTING PREDICTION SCALING")
    print("=" * 60)
    
    # Load points model - check real_basketball_models first
    model_path = "models/real_basketball_models/best_points_model.pt"
    if not Path(model_path).exists():
        model_path = "models/player_props/wnba_points/best_points_model.pt"
        if not Path(model_path).exists():
            model_path = "models/player_props/nba_points/best_points_model.pt"
    
    if Path(model_path).exists():
        try:
            checkpoint = torch.load(model_path, map_location='cpu')
            
            # Get target scaler parameters
            target_params = checkpoint.get('target_scaler_params', {})
            if target_params:
                # Create scaler
                target_scaler = StandardScaler()
                for attr in ['mean_', 'scale_', 'var_', 'n_features_in_', 'n_samples_seen_']:
                    if target_params.get(attr) is not None:
                        setattr(target_scaler, attr, target_params[attr])
                
                # Test scaling/unscaling
                test_values = np.array([5, 10, 15, 20, 25, 30]).reshape(-1, 1)
                scaled = target_scaler.transform(test_values)
                unscaled = target_scaler.inverse_transform(scaled)
                
                print(f"   🧪 Scaling test:")
                print(f"      Original -> Scaled -> Unscaled")
                for orig, sc, unsc in zip(test_values.flatten(), scaled.flatten(), unscaled.flatten()):
                    print(f"      {orig:6.1f} -> {sc:8.3f} -> {unsc:6.1f}")
                
                # Check if scaling is reasonable
                mean_scaled = target_scaler.transform([[15]])[0][0]  # 15 points should be around 0
                print(f"   📊 15 points scales to: {mean_scaled:.3f} (should be near 0)")
                
                if abs(mean_scaled) > 1:
                    print(f"   ⚠️  WARNING: Scaling seems off - 15 points should scale near 0")
                
        except Exception as e:
            print(f"   ❌ Error testing scaling: {e}")
    else:
        print(f"   ❌ No points model found for testing")

if __name__ == "__main__":
    print("🚀 INVESTIGATING NEURAL MODEL TRAINING DATA AND ARCHITECTURE")
    print("=" * 80)
    
    # Step 1: Analyze training data distribution
    training_data = analyze_training_data_distribution()
    
    # Step 2: Analyze model checkpoints
    checkpoint = analyze_model_checkpoints()
    
    # Step 3: Compare model architectures
    compare_model_architectures()
    
    # Step 4: Test prediction scaling
    test_prediction_scaling()
    
    print("\n" + "=" * 80)
    print("🎯 INVESTIGATION COMPLETE")

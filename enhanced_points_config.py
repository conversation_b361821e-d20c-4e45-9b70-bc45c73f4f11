#!/usr/bin/env python3
"""
Enhanced Points Model Configuration

This script creates an optimized configuration for points prediction with:
- Huber loss function for outlier robustness
- Enhanced feature set including usage rate, shot attempts, recent form
- Optimized architecture for high-variance points prediction
"""

import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from neural_cortex.player_props_neural_pipeline import create_player_props_config, PlayerPropsNeuralPipeline
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_enhanced_points_config():
    """Create enhanced configuration for points prediction"""
    
    config = create_player_props_config(
        league="WNBA",
        prop_type="points",

        # Enhanced model architecture for points
        hidden_dim=256,  # Larger network for complex patterns
        num_layers=4,    # Deeper network
        dropout_rate=0.3,  # Higher dropout for regularization

        # Huber loss for outlier robustness
        loss_function="huber",  # Key improvement for points

        # Enhanced training parameters
        learning_rate=0.0005,  # Slightly lower LR for stability
        batch_size=64,
        num_epochs=150,  # More epochs for convergence
        early_stopping_patience=25,     # More patience for complex model

        # Enhanced feature engineering
        include_player_features=True,
        include_matchup_features=True,
        include_situational_features=True,
        include_recent_form=True
    )
    
    logger.info("✅ Created enhanced points configuration with:")
    logger.info(f"  - Loss function: {config.loss_function}")
    logger.info(f"  - Architecture: {config.num_layers} layers, {config.hidden_dim} hidden units")
    logger.info(f"  - Dropout: {config.dropout_rate}")
    logger.info(f"  - Learning rate: {config.learning_rate}")
    logger.info(f"  - Epochs: {config.num_epochs}")

    # Set model save path manually
    config.model_save_path = "models/enhanced_basketball_models"
    
    return config

async def train_enhanced_points_model():
    """Train the enhanced points model with new features"""

    logger.info("🚀 Starting enhanced points model training...")

    # Create enhanced configuration
    config = create_enhanced_points_config()

    # Create pipeline
    pipeline = PlayerPropsNeuralPipeline(config, prop_type="points")

    # Train the model
    logger.info("🔥 Training enhanced points model...")
    results = await pipeline.train()

    logger.info("✅ Enhanced points model training completed!")
    logger.info(f"📊 Training results: {results}")

    return results

async def main():
    """Main async function"""
    # Create the enhanced model directory
    Path("models/enhanced_basketball_models").mkdir(parents=True, exist_ok=True)

    # Train the enhanced points model
    results = await train_enhanced_points_model()

    print("🎯 Enhanced Points Model Training Complete!")
    print(f"📊 Results: {results}")
    print("🔥 Ready to test improved points predictions!")

    return results

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())

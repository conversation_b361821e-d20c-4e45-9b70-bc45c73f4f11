#!/usr/bin/env python3
"""
Fix missing target values in points training data
"""

import pandas as pd
import numpy as np
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_points_targets():
    """Fix missing target values in the points training data"""
    
    data_path = Path("data/real_wnba_points_training_data.csv")
    
    # Read the CSV
    df = pd.read_csv(data_path)
    logger.info(f"📊 Loaded {len(df)} records")
    
    # Check for missing targets
    missing_targets = df['target'].isna().sum()
    logger.info(f"🔍 Found {missing_targets} missing target values")
    
    if missing_targets > 0:
        # Fill missing targets with the points column value
        df['target'] = df['target'].fillna(df['points'])
        logger.info("✅ Fixed missing target values using points column")
    
    # Verify no missing values
    remaining_missing = df['target'].isna().sum()
    if remaining_missing == 0:
        logger.info("✅ All target values are now present")
    else:
        logger.warning(f"⚠️ Still have {remaining_missing} missing target values")
    
    # Save the fixed data
    df.to_csv(data_path, index=False)
    logger.info(f"💾 Saved fixed data to {data_path}")
    
    # Display target statistics
    target_stats = df['target'].describe()
    logger.info(f"📊 Target statistics:")
    logger.info(f"  Mean: {target_stats['mean']:.2f}")
    logger.info(f"  Std: {target_stats['std']:.2f}")
    logger.info(f"  Min: {target_stats['min']:.2f}")
    logger.info(f"  Max: {target_stats['max']:.2f}")
    
    return df

if __name__ == "__main__":
    df = fix_points_targets()
    print(f"✅ Fixed points training data: {df.shape}")
